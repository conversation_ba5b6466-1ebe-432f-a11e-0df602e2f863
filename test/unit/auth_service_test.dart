import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_logistics/features/auth/services/auth_service.dart';
import 'package:flutter_logistics/features/auth/models/auth_models.dart';

void main() {
  group('AuthService Tests', () {
    late AuthService authService;

    setUp(() {
      authService = AuthService();
    });

    test('should initialize without errors', () async {
      expect(() => authService.initialize(), returnsNormally);
    });

    test('should return false for isAuthenticated when not logged in', () {
      expect(authService.isAuthenticated, false);
    });

    test('should return null for currentUser when not logged in', () {
      expect(authService.currentUser, null);
    });

    test('should return null for token when not logged in', () {
      expect(authService.token, null);
    });

    group('Login Tests', () {
      test('should return failure for invalid credentials', () async {
        final result = await authService.login('<EMAIL>', 'wrongpassword');
        expect(result.isSuccess, false);
        expect(result.error, isNotNull);
      });

      test('should validate email format', () {
        // This would test email validation logic
        const validEmail = '<EMAIL>';
        const invalidEmail = 'invalid-email';
        
        expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(validEmail), true);
        expect(RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(invalidEmail), false);
      });
    });

    group('Registration Tests', () {
      test('should create valid RegisterRequest', () {
        final request = RegisterRequest(
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
          role: 'customer',
        );

        expect(request.email, '<EMAIL>');
        expect(request.name, 'Test User');
        expect(request.role, 'customer');
      });

      test('should convert RegisterRequest to JSON', () {
        final request = RegisterRequest(
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
          phone: '0123456789',
          role: 'customer',
        );

        final json = request.toJson();
        expect(json['email'], '<EMAIL>');
        expect(json['name'], 'Test User');
        expect(json['phone'], '0123456789');
        expect(json['role'], 'customer');
      });
    });

    group('AuthResult Tests', () {
      test('should create success result', () {
        final user = UserModel(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'customer',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final result = AuthResult.success(user);
        expect(result.isSuccess, true);
        expect(result.user, user);
        expect(result.error, null);
      });

      test('should create failure result', () {
        const errorMessage = 'Login failed';
        final result = AuthResult.failure(errorMessage);
        
        expect(result.isSuccess, false);
        expect(result.user, null);
        expect(result.error, errorMessage);
      });
    });
  });
}
