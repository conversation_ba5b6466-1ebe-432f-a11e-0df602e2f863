import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_logistics/features/orders/models/order_model.dart';

void main() {
  group('OrderModel Tests', () {
    late OrderModel testOrder;
    late AddressModel pickupAddress;
    late AddressModel deliveryAddress;
    late List<OrderItemModel> items;

    setUp(() {
      pickupAddress = AddressModel(
        street: '123 Pickup Street',
        city: 'Hanoi',
        state: 'Hanoi',
        zipCode: '100000',
        country: 'Vietnam',
        latitude: 21.0285,
        longitude: 105.8542,
      );

      deliveryAddress = AddressModel(
        street: '456 Delivery Avenue',
        city: 'Ho Chi Minh City',
        state: 'Ho Chi Minh',
        zipCode: '700000',
        country: 'Vietnam',
        latitude: 10.8231,
        longitude: 106.6297,
      );

      items = [
        OrderItemModel(
          id: 'item1',
          name: 'Test Product 1',
          description: 'Test product description',
          quantity: 2,
          unitPrice: 100000,
          totalPrice: 200000,
          sku: 'SKU001',
        ),
        OrderItemModel(
          id: 'item2',
          name: 'Test Product 2',
          description: 'Another test product',
          quantity: 1,
          unitPrice: 150000,
          totalPrice: 150000,
          sku: 'SKU002',
        ),
      ];

      testOrder = OrderModel(
        id: 'order123',
        orderNumber: 'ORD-2024-001',
        customerId: 'customer123',
        customerName: 'John Doe',
        customerPhone: '0123456789',
        status: 'pending',
        totalAmount: 350000,
        currency: 'VND',
        pickupAddress: pickupAddress,
        deliveryAddress: deliveryAddress,
        items: items,
        createdAt: DateTime(2024, 1, 1, 10, 0, 0),
        updatedAt: DateTime(2024, 1, 1, 10, 0, 0),
        qrCode: 'ORDER:order123',
      );
    });

    test('should create OrderModel with all required fields', () {
      expect(testOrder.id, 'order123');
      expect(testOrder.orderNumber, 'ORD-2024-001');
      expect(testOrder.customerName, 'John Doe');
      expect(testOrder.status, 'pending');
      expect(testOrder.totalAmount, 350000);
      expect(testOrder.items.length, 2);
    });

    test('should convert OrderModel to JSON', () {
      final json = testOrder.toJson();
      
      expect(json['id'], 'order123');
      expect(json['order_number'], 'ORD-2024-001');
      expect(json['customer_name'], 'John Doe');
      expect(json['status'], 'pending');
      expect(json['total_amount'], 350000);
      expect(json['currency'], 'VND');
      expect(json['items'], isA<List>());
      expect(json['items'].length, 2);
      expect(json['pickup_address'], isA<Map>());
      expect(json['delivery_address'], isA<Map>());
    });

    test('should create OrderModel from JSON', () {
      final json = testOrder.toJson();
      final orderFromJson = OrderModel.fromJson(json);
      
      expect(orderFromJson.id, testOrder.id);
      expect(orderFromJson.orderNumber, testOrder.orderNumber);
      expect(orderFromJson.customerName, testOrder.customerName);
      expect(orderFromJson.status, testOrder.status);
      expect(orderFromJson.totalAmount, testOrder.totalAmount);
      expect(orderFromJson.items.length, testOrder.items.length);
    });

    test('should copy OrderModel with updated fields', () {
      final updatedOrder = testOrder.copyWith(
        status: 'confirmed',
        totalAmount: 400000,
      );
      
      expect(updatedOrder.id, testOrder.id);
      expect(updatedOrder.status, 'confirmed');
      expect(updatedOrder.totalAmount, 400000);
      expect(updatedOrder.customerName, testOrder.customerName);
    });

    group('AddressModel Tests', () {
      test('should create full address string', () {
        expect(pickupAddress.fullAddress, 
               '123 Pickup Street, Hanoi, Hanoi 100000, Vietnam');
      });

      test('should convert AddressModel to/from JSON', () {
        final json = pickupAddress.toJson();
        final addressFromJson = AddressModel.fromJson(json);
        
        expect(addressFromJson.street, pickupAddress.street);
        expect(addressFromJson.city, pickupAddress.city);
        expect(addressFromJson.latitude, pickupAddress.latitude);
        expect(addressFromJson.longitude, pickupAddress.longitude);
      });
    });

    group('OrderItemModel Tests', () {
      test('should create OrderItemModel with correct calculations', () {
        final item = items.first;
        
        expect(item.quantity, 2);
        expect(item.unitPrice, 100000);
        expect(item.totalPrice, 200000);
        expect(item.totalPrice, item.quantity * item.unitPrice);
      });

      test('should convert OrderItemModel to/from JSON', () {
        final item = items.first;
        final json = item.toJson();
        final itemFromJson = OrderItemModel.fromJson(json);
        
        expect(itemFromJson.id, item.id);
        expect(itemFromJson.name, item.name);
        expect(itemFromJson.quantity, item.quantity);
        expect(itemFromJson.unitPrice, item.unitPrice);
        expect(itemFromJson.totalPrice, item.totalPrice);
      });
    });
  });
}
