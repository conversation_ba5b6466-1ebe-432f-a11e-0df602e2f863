# 📚 Flutter Logistics - Hướng dẫn phát triển

## 🎯 Mục tiêu tài liệu
Tài liệu này hướng dẫn chi tiết cách phát triển và mở rộng ứng dụng Flutter Logistics, từ cơ bản đến nâng cao.

## 📋 Nội dung

### 1. [<PERSON><PERSON><PERSON> thức cần có](#kiến-thức-cần-có)
### 2. [Cấu trúc dự án](#cấu-trúc-dự-án)
### 3. [Hướng dẫn từng feature](#hướng-dẫn-từng-feature)
### 4. [Best Practices](#best-practices)
### 5. [Troubleshooting](#troubleshooting)

---

## 🧠 Kiến thức cần có

### C<PERSON> bản
- **Dart**: Ngôn ngữ lập trình của Flutter
- **Flutter**: Framework UI cross-platform
- **Git**: Version control
- **REST API**: <PERSON>iao tiếp với backend

### Nâng cao
- **Clean Architecture**: <PERSON><PERSON><PERSON> trúc phần mềm
- **State Management**: Provider, Riverpod
- **Local Database**: Hive, SQLite
- **Testing**: Unit test, Widget test

---

## 🏗 Cấu trúc dự án

### Core Layer (`lib/core/`)
Chứa các thành phần cốt lõi được sử dụng xuyên suốt ứng dụng:

```dart
// lib/core/constants/app_constants.dart
class AppConstants {
  static const String appName = 'Flutter Logistics';
  static const String baseUrl = 'https://api.logistics.com';
  
  // Order Status
  static const String orderPending = 'pending';
  static const String orderConfirmed = 'confirmed';
  // ...
}
```

### Feature Layer (`lib/features/`)
Mỗi feature là một module độc lập:

#### Authentication Feature (`lib/features/auth/`)
```
auth/
├── models/           # AuthResult, LoginRequest, RegisterRequest
├── services/         # AuthService - business logic
├── screens/          # LoginScreen, RegisterScreen
└── widgets/          # Custom auth widgets
```

#### Orders Feature (`lib/features/orders/`)
```
orders/
├── models/           # OrderModel, AddressModel, OrderItemModel
├── services/         # OrderService - CRUD operations
├── screens/          # OrdersListScreen, OrderDetailScreen
└── widgets/          # OrderCard, OrderFilterSheet
```

### Shared Layer (`lib/shared/`)
Các component được chia sẻ giữa các feature:

```
shared/
├── themes/           # AppTheme - colors, typography
├── widgets/          # CustomTextField, LoadingButton
└── screens/          # SplashScreen, DashboardScreen
```

---

## 🔧 Hướng dẫn từng Feature

### 1. Authentication System

#### Cách hoạt động:
1. User nhập email/password
2. AuthService gửi request đến API
3. Nhận JWT token và user info
4. Lưu vào SharedPreferences
5. Navigate đến Dashboard

#### Code example:
```dart
// lib/features/auth/services/auth_service.dart
class AuthService {
  Future<AuthResult> login(String email, String password) async {
    try {
      final hashedPassword = _hashPassword(password);
      final response = await _dio.post('/auth/login', data: {
        'email': email,
        'password': hashedPassword,
      });
      
      if (response.statusCode == 200) {
        final data = response.data;
        _token = data['token'];
        _currentUser = UserModel.fromJson(data['user']);
        await _saveAuth();
        return AuthResult.success(_currentUser!);
      }
    } catch (e) {
      return AuthResult.failure('Login failed');
    }
  }
}
```

#### Thêm tính năng mới:
1. Tạo model mới trong `models/`
2. Thêm method vào `AuthService`
3. Tạo UI screen trong `screens/`
4. Cập nhật routing trong `app_router.dart`

### 2. QR Code Scanner

#### Cách hoạt động:
1. Mở camera với MobileScanner
2. Detect QR code/barcode
3. Parse kết quả theo format
4. Hiển thị action sheet
5. Thực hiện action tương ứng

#### Code example:
```dart
// lib/features/scanner/services/scanner_service.dart
class ScannerService {
  ScanResultModel parseScanResult(BarcodeCapture capture) {
    final barcode = capture.barcodes.first;
    final rawValue = barcode.rawValue ?? '';
    
    if (rawValue.startsWith('ORDER:')) {
      return ScanResultModel(
        type: ScanType.order,
        value: rawValue.substring(6),
        // ...
      );
    }
    // Handle other formats...
  }
}
```

#### Thêm format QR mới:
1. Thêm enum vào `ScanType`
2. Cập nhật `parseScanResult()` method
3. Thêm action handler trong UI
4. Test với QR code mẫu

### 3. Order Management

#### Cách hoạt động:
1. Load orders từ API
2. Cache vào Hive database
3. Hiển thị danh sách với pagination
4. Support tìm kiếm và filter
5. CRUD operations với sync

#### Code example:
```dart
// lib/features/orders/services/order_service.dart
class OrderService {
  Future<List<OrderModel>> getOrders({
    int page = 1,
    String? status,
    String? search,
  }) async {
    try {
      final response = await _dio.get('/orders', queryParameters: {
        'page': page,
        'status': status,
        'search': search,
      });
      
      final orders = (response.data['orders'] as List)
          .map((json) => OrderModel.fromJson(json))
          .toList();
      
      // Cache locally
      for (final order in orders) {
        await _orderBox.put(order.id, order);
      }
      
      return orders;
    } catch (e) {
      // Return cached data if network fails
      return _getCachedOrders(status: status, search: search);
    }
  }
}
```

#### Thêm tính năng mới:
1. Thêm field vào `OrderModel`
2. Cập nhật API endpoints
3. Thêm UI components
4. Update Hive adapters nếu cần

---

## 💡 Best Practices

### 1. Code Organization
```dart
// ✅ Good: Descriptive naming
class OrderManagementService {
  Future<List<OrderModel>> fetchOrdersByStatus(String status) async {
    // Implementation
  }
}

// ❌ Bad: Generic naming
class DataService {
  Future<List<dynamic>> getData(String type) async {
    // Implementation
  }
}
```

### 2. Error Handling
```dart
// ✅ Good: Proper error handling
try {
  final result = await authService.login(email, password);
  if (result.isSuccess) {
    // Handle success
  } else {
    _showError(result.error ?? 'Unknown error');
  }
} catch (e) {
  _showError('Network error: $e');
}

// ❌ Bad: No error handling
final result = await authService.login(email, password);
// What if this fails?
```

### 3. State Management
```dart
// ✅ Good: Use proper state management
class OrdersNotifier extends ChangeNotifier {
  List<OrderModel> _orders = [];
  bool _isLoading = false;
  
  List<OrderModel> get orders => _orders;
  bool get isLoading => _isLoading;
  
  Future<void> loadOrders() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _orders = await _orderService.getOrders();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}

// ❌ Bad: Direct state mutation in UI
class OrdersScreen extends StatefulWidget {
  @override
  _OrdersScreenState createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  List<OrderModel> orders = [];
  
  @override
  void initState() {
    super.initState();
    // Loading data directly in UI
    _loadOrders();
  }
}
```

### 4. Widget Composition
```dart
// ✅ Good: Small, focused widgets
class OrderCard extends StatelessWidget {
  final OrderModel order;
  final VoidCallback? onTap;
  
  const OrderCard({Key? key, required this.order, this.onTap}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text(order.orderNumber),
        subtitle: Text(order.customerName),
        trailing: _buildStatusChip(),
        onTap: onTap,
      ),
    );
  }
  
  Widget _buildStatusChip() {
    // Status chip implementation
  }
}

// ❌ Bad: Monolithic widget
class OrdersScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.builder(
        itemBuilder: (context, index) {
          final order = orders[index];
          return Card(
            child: Column(
              children: [
                // 100+ lines of UI code here
                // Hard to maintain and test
              ],
            ),
          );
        },
      ),
    );
  }
}
```

---

## 🔍 Troubleshooting

### Common Issues

#### 1. Build Errors
```bash
# Android Gradle issues
flutter clean
flutter pub get
flutter run

# iOS build issues
cd ios
pod install
cd ..
flutter run
```

#### 2. State Not Updating
```dart
// Make sure to call notifyListeners()
class MyNotifier extends ChangeNotifier {
  void updateData() {
    // Update data
    notifyListeners(); // Don't forget this!
  }
}
```

#### 3. Navigation Issues
```dart
// Use context.go() for GoRouter
context.go('/orders/123');

// Not Navigator.push() with GoRouter
Navigator.push(context, MaterialPageRoute(...)); // ❌
```

#### 4. API Integration
```dart
// Check network connectivity
if (await Connectivity().checkConnectivity() == ConnectivityResult.none) {
  // Handle offline mode
  return _getCachedData();
}
```

### Debug Tips
1. Use `print()` or `debugPrint()` for logging
2. Use Flutter Inspector for UI debugging
3. Use `flutter logs` for device logs
4. Use breakpoints in IDE for step debugging

---

## 🚀 Next Steps

### Immediate Tasks
1. Implement remaining CRUD operations
2. Add more QR code formats
3. Improve error handling
4. Add loading states

### Future Enhancements
1. Push notifications
2. Real-time updates with WebSocket
3. Advanced analytics
4. Multi-language support
5. Dark theme
6. Biometric authentication

### Performance Optimization
1. Implement lazy loading
2. Add image caching
3. Optimize database queries
4. Add performance monitoring

---

## 📞 Support

Nếu gặp vấn đề:
1. Kiểm tra [Troubleshooting](#troubleshooting)
2. Xem Flutter documentation
3. Tìm kiếm trên Stack Overflow
4. Tạo issue trên GitHub repository

Happy coding! 🎉
