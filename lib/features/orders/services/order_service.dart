import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:hive/hive.dart';
import '../../../core/constants/app_constants.dart';
import '../models/order_model.dart';

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final Dio _dio = Dio();
  late Box<OrderModel> _orderBox;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    _dio.options.baseUrl = AppConstants.baseUrl + AppConstants.apiVersion;
    _dio.options.connectTimeout = AppConstants.requestTimeout;
    _dio.options.receiveTimeout = AppConstants.requestTimeout;

    // Initialize Hive box for offline storage
    _orderBox = await Hive.openBox<OrderModel>('orders');
    _isInitialized = true;
  }

  // Get all orders with pagination
  Future<List<OrderModel>> getOrders({
    int page = 1,
    int limit = AppConstants.defaultPageSize,
    String? status,
    String? search,
  }) async {
    try {
      // Mock implementation - replace with real API later
      await Future.delayed(Duration(milliseconds: 800)); // Simulate network delay

      final mockOrders = _generateMockOrders();

      // Filter by status if provided
      var filteredOrders = mockOrders;
      if (status != null && status.isNotEmpty) {
        filteredOrders = mockOrders.where((order) => order.status == status).toList();
      }

      // Filter by search if provided
      if (search != null && search.isNotEmpty) {
        filteredOrders = filteredOrders.where((order) =>
          order.orderNumber.toLowerCase().contains(search.toLowerCase()) ||
          order.customerName.toLowerCase().contains(search.toLowerCase())
        ).toList();
      }

      // Sort by creation date (newest first)
      filteredOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Simulate pagination
      final startIndex = (page - 1) * limit;
      final paginatedOrders = filteredOrders.skip(startIndex).take(limit).toList();

      // Cache orders locally
      for (final order in paginatedOrders) {
        await _orderBox.put(order.id, order);
      }

      return paginatedOrders;

      /* Real API implementation - uncomment when you have backend:
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      final response = await _dio.get('/orders', queryParameters: queryParams);

      if (response.statusCode == 200) {
        final List<dynamic> ordersJson = response.data['orders'];
        final orders = ordersJson
            .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
            .toList();

        // Cache orders locally
        for (final order in orders) {
          await _orderBox.put(order.id, order);
        }

        return orders;
      } else {
        throw Exception('Failed to fetch orders');
      }
      */
    } catch (e) {
      // Return cached orders on any error
      return _getCachedOrders(status: status, search: search);
    }
  }

  // Get order by ID
  Future<OrderModel?> getOrderById(String orderId) async {
    try {
      // Mock implementation - replace with real API later
      await Future.delayed(Duration(milliseconds: 500));

      final mockOrders = _generateMockOrders();
      final order = mockOrders.firstWhere(
        (order) => order.id == orderId,
        orElse: () => throw Exception('Order not found'),
      );

      // Cache order locally
      await _orderBox.put(order.id, order);
      return order;

      /* Real API implementation - uncomment when you have backend:
      final response = await _dio.get('/orders/$orderId');

      if (response.statusCode == 200) {
        final order = OrderModel.fromJson(response.data['order']);
        await _orderBox.put(order.id, order);
        return order;
      } else {
        return null;
      }
      */
    } catch (e) {
      // Return cached order if available
      return _orderBox.get(orderId);
    }
  }

  // Create new order
  Future<OrderModel?> createOrder(Map<String, dynamic> orderData) async {
    try {
      final response = await _dio.post('/orders', data: orderData);

      if (response.statusCode == 201) {
        final order = OrderModel.fromJson(response.data['order']);
        await _orderBox.put(order.id, order);
        return order;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // Update order status
  Future<bool> updateOrderStatus(String orderId, String status, {String? notes}) async {
    try {
      // Mock implementation - replace with real API later
      await Future.delayed(Duration(milliseconds: 800));

      // Get current order from cache or mock data
      var order = _orderBox.get(orderId);
      if (order == null) {
        final mockOrders = _generateMockOrders();
        order = mockOrders.firstWhere(
          (o) => o.id == orderId,
          orElse: () => throw Exception('Order not found'),
        );
      }

      // Update order with new status
      final updatedOrder = order.copyWith(
        status: status,
        updatedAt: DateTime.now(),
        notes: notes,
      );

      // Save updated order to cache
      await _orderBox.put(updatedOrder.id, updatedOrder);

      return true;

      /* Real API implementation - uncomment when you have backend:
      final response = await _dio.put('/orders/$orderId/status', data: {
        'status': status,
        'notes': notes,
        'updated_at': DateTime.now().toIso8601String(),
      });

      if (response.statusCode == 200) {
        final updatedOrder = OrderModel.fromJson(response.data['order']);
        await _orderBox.put(updatedOrder.id, updatedOrder);
        return true;
      } else {
        return false;
      }
      */
    } catch (e) {
      print('Error updating order status: $e');
      return false;
    }
  }

  // Update order
  Future<OrderModel?> updateOrder(String orderId, Map<String, dynamic> updateData) async {
    try {
      final response = await _dio.put('/orders/$orderId', data: updateData);

      if (response.statusCode == 200) {
        final order = OrderModel.fromJson(response.data['order']);
        await _orderBox.put(order.id, order);
        return order;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // Delete order
  Future<bool> deleteOrder(String orderId) async {
    try {
      final response = await _dio.delete('/orders/$orderId');

      if (response.statusCode == 200) {
        await _orderBox.delete(orderId);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // Get orders by status
  Future<List<OrderModel>> getOrdersByStatus(String status) async {
    return getOrders(status: status);
  }

  // Search orders
  Future<List<OrderModel>> searchOrders(String query) async {
    return getOrders(search: query);
  }

  // Get order statistics
  Future<Map<String, int>> getOrderStatistics() async {
    try {
      // Mock implementation - replace with real API later
      await Future.delayed(Duration(milliseconds: 500));

      final mockOrders = _generateMockOrders();
      final stats = <String, int>{};

      for (final status in [
        AppConstants.orderPending,
        AppConstants.orderConfirmed,
        AppConstants.orderInTransit,
        AppConstants.orderDelivered,
        AppConstants.orderCancelled,
      ]) {
        stats[status] = mockOrders.where((order) => order.status == status).length;
      }

      return stats;

      /* Real API implementation - uncomment when you have backend:
      final response = await _dio.get('/orders/statistics');

      if (response.statusCode == 200) {
        return Map<String, int>.from(response.data['statistics']);
      } else {
        return {};
      }
      */
    } catch (e) {
      // Return cached statistics
      return _getCachedStatistics();
    }
  }

  // Get orders for current user (driver/customer)
  Future<List<OrderModel>> getMyOrders() async {
    try {
      final response = await _dio.get('/orders/my');

      if (response.statusCode == 200) {
        final List<dynamic> ordersJson = response.data['orders'];
        final orders = ordersJson
            .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
            .toList();

        // Cache orders locally
        for (final order in orders) {
          await _orderBox.put(order.id, order);
        }

        return orders;
      } else {
        return [];
      }
    } catch (e) {
      return _getCachedOrders();
    }
  }

  // Assign driver to order
  Future<bool> assignDriver(String orderId, String driverId) async {
    try {
      final response = await _dio.put('/orders/$orderId/assign', data: {
        'driver_id': driverId,
      });

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // Upload order images
  Future<List<String>> uploadOrderImages(String orderId, List<String> imagePaths) async {
    try {
      final formData = FormData();
      
      for (int i = 0; i < imagePaths.length; i++) {
        formData.files.add(MapEntry(
          'images',
          await MultipartFile.fromFile(imagePaths[i]),
        ));
      }

      final response = await _dio.post('/orders/$orderId/images', data: formData);

      if (response.statusCode == 200) {
        return List<String>.from(response.data['image_urls']);
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  // Private methods for caching
  List<OrderModel> _getCachedOrders({String? status, String? search}) {
    var orders = _orderBox.values.toList();

    if (status != null && status.isNotEmpty) {
      orders = orders.where((order) => order.status == status).toList();
    }

    if (search != null && search.isNotEmpty) {
      orders = orders.where((order) =>
          order.orderNumber.toLowerCase().contains(search.toLowerCase()) ||
          order.customerName.toLowerCase().contains(search.toLowerCase())).toList();
    }

    // Sort by creation date (newest first)
    orders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return orders;
  }

  Map<String, int> _getCachedStatistics() {
    final orders = _orderBox.values.toList();
    final stats = <String, int>{};

    for (final status in [
      AppConstants.orderPending,
      AppConstants.orderConfirmed,
      AppConstants.orderInTransit,
      AppConstants.orderDelivered,
      AppConstants.orderCancelled,
    ]) {
      stats[status] = orders.where((order) => order.status == status).length;
    }

    return stats;
  }

  // Clear cache
  Future<void> clearCache() async {
    await _orderBox.clear();
  }

  // Sync offline changes
  Future<void> syncOfflineChanges() async {
    // Implementation for syncing offline changes when connection is restored
    // This would involve tracking local changes and uploading them to server
  }

  // Generate mock orders for testing
  List<OrderModel> _generateMockOrders() {
    final now = DateTime.now();

    return [
      OrderModel(
        id: 'order_001',
        orderNumber: 'ORD-2024-001',
        customerId: 'customer_001',
        customerName: 'Nguyễn Văn An',
        customerPhone: '0123456789',
        status: AppConstants.orderPending,
        totalAmount: 250000,
        currency: 'VND',
        pickupAddress: AddressModel(
          street: '123 Đường Láng',
          city: 'Hà Nội',
          state: 'Hà Nội',
          zipCode: '100000',
          country: 'Việt Nam',
          latitude: 21.0285,
          longitude: 105.8542,
        ),
        deliveryAddress: AddressModel(
          street: '456 Nguyễn Huệ',
          city: 'TP.HCM',
          state: 'TP.HCM',
          zipCode: '700000',
          country: 'Việt Nam',
          latitude: 10.8231,
          longitude: 106.6297,
        ),
        items: [
          OrderItemModel(
            id: 'item_001',
            name: 'Laptop Dell XPS 13',
            description: 'Laptop cao cấp cho doanh nhân',
            quantity: 1,
            unitPrice: 250000,
            totalPrice: 250000,
            sku: 'LAPTOP001',
          ),
        ],
        createdAt: now.subtract(Duration(hours: 2)),
        updatedAt: now.subtract(Duration(hours: 2)),
        qrCode: 'ORDER:order_001',
      ),
      OrderModel(
        id: 'order_002',
        orderNumber: 'ORD-2024-002',
        customerId: 'customer_002',
        customerName: 'Trần Thị Bình',
        customerPhone: '0987654321',
        status: AppConstants.orderConfirmed,
        totalAmount: 150000,
        currency: 'VND',
        pickupAddress: AddressModel(
          street: '789 Cầu Giấy',
          city: 'Hà Nội',
          state: 'Hà Nội',
          zipCode: '100000',
          country: 'Việt Nam',
        ),
        deliveryAddress: AddressModel(
          street: '321 Lê Lợi',
          city: 'Đà Nẵng',
          state: 'Đà Nẵng',
          zipCode: '550000',
          country: 'Việt Nam',
        ),
        items: [
          OrderItemModel(
            id: 'item_002',
            name: 'Điện thoại iPhone 15',
            description: 'Điện thoại thông minh mới nhất',
            quantity: 1,
            unitPrice: 150000,
            totalPrice: 150000,
            sku: 'PHONE001',
          ),
        ],
        createdAt: now.subtract(Duration(hours: 5)),
        updatedAt: now.subtract(Duration(hours: 1)),
        driverId: 'driver_001',
        driverName: 'Lê Văn Tài',
        qrCode: 'ORDER:order_002',
      ),
      OrderModel(
        id: 'order_003',
        orderNumber: 'ORD-2024-003',
        customerId: 'customer_003',
        customerName: 'Phạm Minh Cường',
        customerPhone: '0369852147',
        status: AppConstants.orderInTransit,
        totalAmount: 350000,
        currency: 'VND',
        pickupAddress: AddressModel(
          street: '555 Hoàng Hoa Thám',
          city: 'TP.HCM',
          state: 'TP.HCM',
          zipCode: '700000',
          country: 'Việt Nam',
        ),
        deliveryAddress: AddressModel(
          street: '777 Trần Phú',
          city: 'Hải Phòng',
          state: 'Hải Phòng',
          zipCode: '180000',
          country: 'Việt Nam',
        ),
        items: [
          OrderItemModel(
            id: 'item_003',
            name: 'Máy tính bảng iPad Pro',
            description: 'Máy tính bảng chuyên nghiệp',
            quantity: 1,
            unitPrice: 350000,
            totalPrice: 350000,
            sku: 'TABLET001',
          ),
        ],
        createdAt: now.subtract(Duration(days: 1)),
        updatedAt: now.subtract(Duration(minutes: 30)),
        driverId: 'driver_002',
        driverName: 'Hoàng Văn Sơn',
        estimatedDelivery: now.add(Duration(hours: 4)),
        qrCode: 'ORDER:order_003',
      ),
      OrderModel(
        id: 'order_004',
        orderNumber: 'ORD-2024-004',
        customerId: 'customer_004',
        customerName: 'Vũ Thị Dung',
        customerPhone: '0741852963',
        status: AppConstants.orderDelivered,
        totalAmount: 180000,
        currency: 'VND',
        pickupAddress: AddressModel(
          street: '999 Nguyễn Trãi',
          city: 'Hà Nội',
          state: 'Hà Nội',
          zipCode: '100000',
          country: 'Việt Nam',
        ),
        deliveryAddress: AddressModel(
          street: '111 Lý Thường Kiệt',
          city: 'Hà Nội',
          state: 'Hà Nội',
          zipCode: '100000',
          country: 'Việt Nam',
        ),
        items: [
          OrderItemModel(
            id: 'item_004',
            name: 'Tai nghe AirPods Pro',
            description: 'Tai nghe không dây cao cấp',
            quantity: 1,
            unitPrice: 180000,
            totalPrice: 180000,
            sku: 'HEADPHONE001',
          ),
        ],
        createdAt: now.subtract(Duration(days: 2)),
        updatedAt: now.subtract(Duration(hours: 6)),
        driverId: 'driver_001',
        driverName: 'Lê Văn Tài',
        estimatedDelivery: now.subtract(Duration(hours: 8)),
        actualDelivery: now.subtract(Duration(hours: 6)),
        qrCode: 'ORDER:order_004',
      ),
      OrderModel(
        id: 'order_005',
        orderNumber: 'ORD-2024-005',
        customerId: 'customer_005',
        customerName: 'Đỗ Thanh Long',
        customerPhone: '0852963741',
        status: AppConstants.orderCancelled,
        totalAmount: 120000,
        currency: 'VND',
        pickupAddress: AddressModel(
          street: '333 Hai Bà Trưng',
          city: 'TP.HCM',
          state: 'TP.HCM',
          zipCode: '700000',
          country: 'Việt Nam',
        ),
        deliveryAddress: AddressModel(
          street: '444 Điện Biên Phủ',
          city: 'TP.HCM',
          state: 'TP.HCM',
          zipCode: '700000',
          country: 'Việt Nam',
        ),
        items: [
          OrderItemModel(
            id: 'item_005',
            name: 'Đồng hồ thông minh Apple Watch',
            description: 'Đồng hồ thông minh Series 9',
            quantity: 1,
            unitPrice: 120000,
            totalPrice: 120000,
            sku: 'WATCH001',
          ),
        ],
        createdAt: now.subtract(Duration(days: 3)),
        updatedAt: now.subtract(Duration(days: 2)),
        notes: 'Khách hàng hủy do thay đổi ý định',
        qrCode: 'ORDER:order_005',
      ),
    ];
  }
}
