import 'package:hive/hive.dart';

part 'order_model.g.dart';

@HiveType(typeId: 1)
class OrderModel extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String orderNumber;
  
  @HiveField(2)
  final String customerId;
  
  @HiveField(3)
  final String customerName;
  
  @HiveField(4)
  final String customerPhone;
  
  @HiveField(5)
  final String status;
  
  @HiveField(6)
  final double totalAmount;
  
  @HiveField(7)
  final String currency;
  
  @HiveField(8)
  final AddressModel pickupAddress;
  
  @HiveField(9)
  final AddressModel deliveryAddress;
  
  @HiveField(10)
  final List<OrderItemModel> items;
  
  @HiveField(11)
  final DateTime createdAt;
  
  @HiveField(12)
  final DateTime updatedAt;
  
  @HiveField(13)
  final DateTime? estimatedDelivery;
  
  @HiveField(14)
  final DateTime? actualDelivery;
  
  @HiveField(15)
  final String? driverId;
  
  @HiveField(16)
  final String? driverName;
  
  @HiveField(17)
  final String? notes;
  
  @HiveField(18)
  final List<String>? images;
  
  @HiveField(19)
  final String? qrCode;

  OrderModel({
    required this.id,
    required this.orderNumber,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.status,
    required this.totalAmount,
    this.currency = 'VND',
    required this.pickupAddress,
    required this.deliveryAddress,
    required this.items,
    required this.createdAt,
    required this.updatedAt,
    this.estimatedDelivery,
    this.actualDelivery,
    this.driverId,
    this.driverName,
    this.notes,
    this.images,
    this.qrCode,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String,
      customerId: json['customer_id'] as String,
      customerName: json['customer_name'] as String,
      customerPhone: json['customer_phone'] as String,
      status: json['status'] as String,
      totalAmount: (json['total_amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'VND',
      pickupAddress: AddressModel.fromJson(json['pickup_address'] as Map<String, dynamic>),
      deliveryAddress: AddressModel.fromJson(json['delivery_address'] as Map<String, dynamic>),
      items: (json['items'] as List<dynamic>)
          .map((item) => OrderItemModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      estimatedDelivery: json['estimated_delivery'] != null
          ? DateTime.parse(json['estimated_delivery'] as String)
          : null,
      actualDelivery: json['actual_delivery'] != null
          ? DateTime.parse(json['actual_delivery'] as String)
          : null,
      driverId: json['driver_id'] as String?,
      driverName: json['driver_name'] as String?,
      notes: json['notes'] as String?,
      images: (json['images'] as List<dynamic>?)?.cast<String>(),
      qrCode: json['qr_code'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'status': status,
      'total_amount': totalAmount,
      'currency': currency,
      'pickup_address': pickupAddress.toJson(),
      'delivery_address': deliveryAddress.toJson(),
      'items': items.map((item) => item.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'estimated_delivery': estimatedDelivery?.toIso8601String(),
      'actual_delivery': actualDelivery?.toIso8601String(),
      'driver_id': driverId,
      'driver_name': driverName,
      'notes': notes,
      'images': images,
      'qr_code': qrCode,
    };
  }

  OrderModel copyWith({
    String? id,
    String? orderNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? status,
    double? totalAmount,
    String? currency,
    AddressModel? pickupAddress,
    AddressModel? deliveryAddress,
    List<OrderItemModel>? items,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? estimatedDelivery,
    DateTime? actualDelivery,
    String? driverId,
    String? driverName,
    String? notes,
    List<String>? images,
    String? qrCode,
  }) {
    return OrderModel(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      status: status ?? this.status,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      actualDelivery: actualDelivery ?? this.actualDelivery,
      driverId: driverId ?? this.driverId,
      driverName: driverName ?? this.driverName,
      notes: notes ?? this.notes,
      images: images ?? this.images,
      qrCode: qrCode ?? this.qrCode,
    );
  }
}

@HiveType(typeId: 2)
class AddressModel extends HiveObject {
  @HiveField(0)
  final String street;
  
  @HiveField(1)
  final String city;
  
  @HiveField(2)
  final String state;
  
  @HiveField(3)
  final String zipCode;
  
  @HiveField(4)
  final String country;
  
  @HiveField(5)
  final double? latitude;
  
  @HiveField(6)
  final double? longitude;

  AddressModel({
    required this.street,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
    this.latitude,
    this.longitude,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zip_code'] as String,
      country: json['country'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  String get fullAddress => '$street, $city, $state $zipCode, $country';
}

@HiveType(typeId: 3)
class OrderItemModel extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String description;
  
  @HiveField(3)
  final int quantity;
  
  @HiveField(4)
  final double unitPrice;
  
  @HiveField(5)
  final double totalPrice;
  
  @HiveField(6)
  final String? sku;
  
  @HiveField(7)
  final String? image;

  OrderItemModel({
    required this.id,
    required this.name,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.sku,
    this.image,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    return OrderItemModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      quantity: json['quantity'] as int,
      unitPrice: (json['unit_price'] as num).toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
      sku: json['sku'] as String?,
      image: json['image'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'sku': sku,
      'image': image,
    };
  }
}
