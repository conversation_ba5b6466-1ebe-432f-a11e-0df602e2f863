import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../core/constants/app_constants.dart';
import '../models/order_model.dart';

class OrderStatusTimeline extends StatelessWidget {
  final OrderModel order;

  const OrderStatusTimeline({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final timelineItems = _buildTimelineItems();

    return Column(
      children: timelineItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isLast = index == timelineItems.length - 1;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timeline indicator
            Column(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: item.isCompleted 
                        ? item.color 
                        : theme.colorScheme.surfaceVariant,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: item.isCompleted 
                          ? item.color 
                          : theme.colorScheme.outline,
                      width: 2,
                    ),
                  ),
                  child: item.isCompleted
                      ? Icon(
                          item.icon,
                          size: 12,
                          color: Colors.white,
                        )
                      : null,
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 40,
                    color: item.isCompleted
                        ? item.color.withOpacity(0.3)
                        : theme.colorScheme.outline.withOpacity(0.3),
                  ),
              ],
            ),
            const SizedBox(width: 16),
            
            // Timeline content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: item.isCompleted 
                            ? item.color 
                            : theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    if (item.subtitle != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        item.subtitle!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                    if (item.timestamp != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        DateFormat('dd/MM/yyyy HH:mm').format(item.timestamp!),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  List<TimelineItem> _buildTimelineItems() {
    final items = <TimelineItem>[];
    final currentStatus = order.status;

    // Pending
    items.add(TimelineItem(
      title: 'Đơn hàng được tạo',
      subtitle: 'Đơn hàng đã được tạo và chờ xử lý',
      icon: Icons.receipt_long,
      color: AppTheme.pendingColor,
      isCompleted: true,
      timestamp: order.createdAt,
    ));

    // Confirmed
    items.add(TimelineItem(
      title: 'Đã xác nhận',
      subtitle: currentStatus == AppConstants.orderPending 
          ? 'Chờ xác nhận đơn hàng'
          : 'Đơn hàng đã được xác nhận',
      icon: Icons.check_circle,
      color: AppTheme.confirmedColor,
      isCompleted: _isStatusCompleted(AppConstants.orderConfirmed, currentStatus),
      timestamp: _isStatusCompleted(AppConstants.orderConfirmed, currentStatus) 
          ? order.updatedAt 
          : null,
    ));

    // In Transit
    items.add(TimelineItem(
      title: 'Đang giao hàng',
      subtitle: _getInTransitSubtitle(currentStatus),
      icon: Icons.local_shipping,
      color: AppTheme.inTransitColor,
      isCompleted: _isStatusCompleted(AppConstants.orderInTransit, currentStatus),
      timestamp: _isStatusCompleted(AppConstants.orderInTransit, currentStatus) 
          ? order.updatedAt 
          : null,
    ));

    // Delivered or Cancelled
    if (currentStatus == AppConstants.orderDelivered) {
      items.add(TimelineItem(
        title: 'Đã giao thành công',
        subtitle: 'Đơn hàng đã được giao đến khách hàng',
        icon: Icons.check_circle,
        color: AppTheme.deliveredColor,
        isCompleted: true,
        timestamp: order.actualDelivery ?? order.updatedAt,
      ));
    } else if (currentStatus == AppConstants.orderCancelled) {
      items.add(TimelineItem(
        title: 'Đã hủy',
        subtitle: order.notes ?? 'Đơn hàng đã bị hủy',
        icon: Icons.cancel,
        color: AppTheme.cancelledColor,
        isCompleted: true,
        timestamp: order.updatedAt,
      ));
    } else {
      items.add(TimelineItem(
        title: 'Giao hàng thành công',
        subtitle: 'Chờ giao hàng đến khách hàng',
        icon: Icons.check_circle,
        color: AppTheme.deliveredColor,
        isCompleted: false,
        timestamp: order.estimatedDelivery,
      ));
    }

    return items;
  }

  bool _isStatusCompleted(String targetStatus, String currentStatus) {
    final statusOrder = [
      AppConstants.orderPending,
      AppConstants.orderConfirmed,
      AppConstants.orderInTransit,
      AppConstants.orderDelivered,
    ];

    final targetIndex = statusOrder.indexOf(targetStatus);
    final currentIndex = statusOrder.indexOf(currentStatus);

    if (currentStatus == AppConstants.orderCancelled) {
      // If cancelled, only pending is completed
      return targetStatus == AppConstants.orderPending;
    }

    return currentIndex >= targetIndex;
  }

  String _getInTransitSubtitle(String currentStatus) {
    switch (currentStatus) {
      case AppConstants.orderPending:
        return 'Chờ xác nhận để bắt đầu giao hàng';
      case AppConstants.orderConfirmed:
        return 'Chờ tài xế nhận đơn và bắt đầu giao';
      case AppConstants.orderInTransit:
        return order.driverName != null 
            ? 'Tài xế ${order.driverName} đang giao hàng'
            : 'Đang trên đường giao hàng';
      case AppConstants.orderDelivered:
        return 'Đã hoàn thành giao hàng';
      case AppConstants.orderCancelled:
        return 'Đơn hàng đã bị hủy';
      default:
        return 'Đang xử lý';
    }
  }
}

class TimelineItem {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final bool isCompleted;
  final DateTime? timestamp;

  TimelineItem({
    required this.title,
    this.subtitle,
    required this.icon,
    required this.color,
    required this.isCompleted,
    this.timestamp,
  });
}
