import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../core/constants/app_constants.dart';
import '../models/order_model.dart';

class OrderCard extends StatelessWidget {
  final OrderModel order;
  final VoidCallback? onTap;
  final Function(String)? onStatusUpdate;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onStatusUpdate,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with order number and status
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.orderNumber,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Khách hàng: ${order.customerName}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Order details
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      Icons.attach_money,
                      'Tổng tiền',
                      currencyFormat.format(order.totalAmount),
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      Icons.inventory_2,
                      'Số lượng',
                      '${order.items.length} sản phẩm',
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Addresses
              _buildAddressInfo(context),
              
              const SizedBox(height: 12),
              
              // Footer with date and actions
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('dd/MM/yyyy HH:mm').format(order.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const Spacer(),
                  if (onStatusUpdate != null) _buildQuickActions(context),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = AppTheme.getOrderStatusColor(order.status);
    final statusIcon = AppTheme.getOrderStatusIcon(order.status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: statusColor.withOpacity(0.4),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: statusColor.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              statusIcon,
              size: 14,
              color: statusColor,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            _getStatusDisplayName(order.status),
            style: theme.textTheme.bodySmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w700,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, IconData icon, String label, String value) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAddressInfo(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.location_on,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Từ: ${order.pickupAddress.street}, ${order.pickupAddress.city}',
                    style: theme.textTheme.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Đến: ${order.deliveryAddress.street}, ${order.deliveryAddress.city}',
                    style: theme.textTheme.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    final theme = Theme.of(context);
    
    return PopupMenuButton<String>(
      onSelected: (value) {
        if (onStatusUpdate != null) {
          onStatusUpdate!(value);
        }
      },
      itemBuilder: (context) => [
        if (order.status == AppConstants.orderPending)
          PopupMenuItem(
            value: AppConstants.orderConfirmed,
            child: Row(
              children: [
                Icon(Icons.check_circle_outline, color: AppTheme.confirmedColor),
                const SizedBox(width: 8),
                const Text('Xác nhận'),
              ],
            ),
          ),
        if (order.status == AppConstants.orderConfirmed)
          PopupMenuItem(
            value: AppConstants.orderInTransit,
            child: Row(
              children: [
                Icon(Icons.local_shipping, color: AppTheme.inTransitColor),
                const SizedBox(width: 8),
                const Text('Bắt đầu giao'),
              ],
            ),
          ),
        if (order.status == AppConstants.orderInTransit)
          PopupMenuItem(
            value: AppConstants.orderDelivered,
            child: Row(
              children: [
                Icon(Icons.check_circle, color: AppTheme.deliveredColor),
                const SizedBox(width: 8),
                const Text('Đã giao'),
              ],
            ),
          ),
        if (order.status != AppConstants.orderCancelled && 
            order.status != AppConstants.orderDelivered)
          PopupMenuItem(
            value: AppConstants.orderCancelled,
            child: Row(
              children: [
                Icon(Icons.cancel, color: AppTheme.cancelledColor),
                const SizedBox(width: 8),
                const Text('Hủy đơn'),
              ],
            ),
          ),
      ],
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.more_vert,
          size: 16,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case AppConstants.orderPending:
        return 'Chờ xử lý';
      case AppConstants.orderConfirmed:
        return 'Đã xác nhận';
      case AppConstants.orderInTransit:
        return 'Đang giao';
      case AppConstants.orderDelivered:
        return 'Đã giao';
      case AppConstants.orderCancelled:
        return 'Đã hủy';
      default:
        return 'Không xác định';
    }
  }
}
