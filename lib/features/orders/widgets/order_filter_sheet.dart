import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../shared/themes/app_theme.dart';

class OrderFilterSheet extends StatefulWidget {
  final String? currentStatus;
  final Function(String?) onFilterApplied;

  const OrderFilterSheet({
    super.key,
    this.currentStatus,
    required this.onFilterApplied,
  });

  @override
  State<OrderFilterSheet> createState() => _OrderFilterSheetState();
}

class _OrderFilterSheetState extends State<OrderFilterSheet> {
  String? _selectedStatus;

  final List<Map<String, dynamic>> _statusOptions = [
    {
      'value': null,
      'label': 'Tất cả đơn hàng',
      'icon': Icons.list_alt,
      'color': Colors.grey,
    },
    {
      'value': AppConstants.orderPending,
      'label': 'Chờ xử lý',
      'icon': Icons.schedule,
      'color': AppTheme.pendingColor,
    },
    {
      'value': AppConstants.orderConfirmed,
      'label': 'Đã xác nhận',
      'icon': Icons.check_circle_outline,
      'color': AppTheme.confirmedColor,
    },
    {
      'value': AppConstants.orderInTransit,
      'label': 'Đang giao hàng',
      'icon': Icons.local_shipping,
      'color': AppTheme.inTransitColor,
    },
    {
      'value': AppConstants.orderDelivered,
      'label': 'Đã giao thành công',
      'icon': Icons.check_circle,
      'color': AppTheme.deliveredColor,
    },
    {
      'value': AppConstants.orderCancelled,
      'label': 'Đã hủy',
      'icon': Icons.cancel,
      'color': AppTheme.cancelledColor,
    },
  ];

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.currentStatus;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Icon(
                  Icons.filter_list,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Text(
                  'Lọc đơn hàng',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedStatus = null;
                    });
                  },
                  child: const Text('Xóa bộ lọc'),
                ),
              ],
            ),
          ),
          
          // Status options
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Trạng thái đơn hàng',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                
                ..._statusOptions.map((option) {
                  final isSelected = _selectedStatus == option['value'];
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectedStatus = option['value'];
                        });
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? theme.colorScheme.primary.withOpacity(0.1)
                              : theme.colorScheme.surfaceVariant.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.outline.withOpacity(0.3),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: option['color'].withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                option['icon'],
                                color: option['color'],
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                option['label'],
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  fontWeight: isSelected 
                                      ? FontWeight.w600 
                                      : FontWeight.normal,
                                  color: isSelected
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.onSurface,
                                ),
                              ),
                            ),
                            if (isSelected)
                              Icon(
                                Icons.check_circle,
                                color: theme.colorScheme.primary,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Hủy'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onFilterApplied(_selectedStatus);
                      Navigator.of(context).pop();
                    },
                    child: const Text('Áp dụng'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
