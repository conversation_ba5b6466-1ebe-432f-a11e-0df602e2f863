import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../core/constants/app_constants.dart';
import '../services/order_service.dart';
import '../models/order_model.dart';
import '../widgets/order_card.dart';
import '../widgets/order_filter_sheet.dart';

class OrdersListScreen extends StatefulWidget {
  const OrdersListScreen({super.key});

  @override
  State<OrdersListScreen> createState() => _OrdersListScreenState();
}

class _OrdersListScreenState extends State<OrdersListScreen>
    with TickerProviderStateMixin {
  final OrderService _orderService = OrderService();
  final RefreshController _refreshController = RefreshController();
  final TextEditingController _searchController = TextEditingController();
  
  late TabController _tabController;
  List<OrderModel> _orders = [];
  Map<String, int> _statistics = {};
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _selectedStatus;
  String _searchQuery = '';
  int _currentPage = 1;
  bool _hasMoreData = true;

  final List<Map<String, dynamic>> _statusTabs = [
    {'status': null, 'label': 'Tất cả', 'icon': Icons.list_alt},
    {'status': AppConstants.orderPending, 'label': 'Chờ xử lý', 'icon': Icons.schedule},
    {'status': AppConstants.orderConfirmed, 'label': 'Đã xác nhận', 'icon': Icons.check_circle_outline},
    {'status': AppConstants.orderInTransit, 'label': 'Đang giao', 'icon': Icons.local_shipping},
    {'status': AppConstants.orderDelivered, 'label': 'Đã giao', 'icon': Icons.check_circle},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _statusTabs.length, vsync: this);
    _tabController.addListener(_onTabChanged);
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    await _orderService.initialize();
    await _loadOrders();
    await _loadStatistics();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      final selectedTab = _statusTabs[_tabController.index];
      setState(() {
        _selectedStatus = selectedTab['status'];
        _currentPage = 1;
        _hasMoreData = true;
      });
      _loadOrders(refresh: true);
    }
  }

  Future<void> _loadOrders({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _isLoading = true;
        _currentPage = 1;
        _hasMoreData = true;
      });
    } else if (_isLoadingMore || !_hasMoreData) {
      return;
    }

    if (!refresh) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final orders = await _orderService.getOrders(
        page: _currentPage,
        status: _selectedStatus,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      setState(() {
        if (refresh || _currentPage == 1) {
          _orders = orders;
        } else {
          _orders.addAll(orders);
        }
        
        _hasMoreData = orders.length >= AppConstants.defaultPageSize;
        _currentPage++;
        _isLoading = false;
        _isLoadingMore = false;
      });

      if (refresh) {
        _refreshController.refreshCompleted();
      } else {
        _refreshController.loadComplete();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
      });
      
      if (refresh) {
        _refreshController.refreshFailed();
      } else {
        _refreshController.loadFailed();
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi tải dữ liệu: $e')),
        );
      }
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await _orderService.getOrderStatistics();
      setState(() {
        _statistics = stats;
      });
    } catch (e) {
      // Handle error silently for statistics
    }
  }

  void _onSearch(String query) {
    setState(() {
      _searchQuery = query;
      _currentPage = 1;
      _hasMoreData = true;
    });
    _loadOrders(refresh: true);
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => OrderFilterSheet(
        currentStatus: _selectedStatus,
        onFilterApplied: (status) {
          setState(() {
            _selectedStatus = status;
            _currentPage = 1;
            _hasMoreData = true;
          });
          _loadOrders(refresh: true);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quản lý đơn hàng'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showFilterSheet,
            icon: const Icon(Icons.filter_list),
            tooltip: 'Lọc đơn hàng',
          ),
          IconButton(
            onPressed: () => context.push('/scanner'),
            icon: const Icon(Icons.qr_code_scanner),
            tooltip: 'Quét mã QR',
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(100),
          child: Column(
            children: [
              // Search Bar
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Tìm kiếm đơn hàng...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              _onSearch('');
                            },
                            icon: const Icon(Icons.clear),
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                  ),
                  onChanged: _onSearch,
                ),
              ),
              
              // Status Tabs
              Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  border: Border(
                    bottom: BorderSide(
                      color: theme.colorScheme.outline.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                ),
                child: TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  indicatorColor: theme.colorScheme.primary,
                  indicatorWeight: 3,
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelColor: theme.colorScheme.primary,
                  unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.6),
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  overlayColor: MaterialStateProperty.resolveWith<Color?>(
                    (Set<MaterialState> states) {
                      if (states.contains(MaterialState.hovered)) {
                        return theme.colorScheme.primary.withOpacity(0.08);
                      }
                      if (states.contains(MaterialState.pressed)) {
                        return theme.colorScheme.primary.withOpacity(0.12);
                      }
                      return null;
                    },
                  ),
                  tabs: _statusTabs.asMap().entries.map((entry) {
                    final index = entry.key;
                    final tab = entry.value;
                    final count = _statistics[tab['status']] ?? 0;

                    return Tab(
                      child: AnimatedBuilder(
                        animation: _tabController,
                        builder: (context, child) {
                          final isSelected = _tabController.index == index;

                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? theme.colorScheme.primary.withOpacity(0.1)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(20),
                              border: isSelected
                                  ? Border.all(
                                      color: theme.colorScheme.primary.withOpacity(0.3),
                                      width: 1,
                                    )
                                  : null,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  tab['icon'],
                                  size: 16,
                                  color: isSelected
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.onSurface.withOpacity(0.6),
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  tab['label'],
                                  style: TextStyle(
                                    color: isSelected
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.onSurface.withOpacity(0.6),
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                                  ),
                                ),
                                if (count > 0) ...[
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? theme.colorScheme.primary
                                          : theme.colorScheme.outline.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(
                                      count.toString(),
                                      style: TextStyle(
                                        color: isSelected
                                            ? theme.colorScheme.onPrimary
                                            : theme.colorScheme.onSurface.withOpacity(0.8),
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SmartRefresher(
              controller: _refreshController,
              enablePullDown: true,
              enablePullUp: _hasMoreData,
              onRefresh: () => _loadOrders(refresh: true),
              onLoading: () => _loadOrders(),
              child: _orders.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _orders.length + (_isLoadingMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index == _orders.length) {
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }

                        final order = _orders[index];
                        return OrderCard(
                          order: order,
                          onTap: () => context.push('/orders/${order.id}'),
                          onStatusUpdate: (newStatus) => _updateOrderStatus(order, newStatus),
                        );
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/orders/create'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'Không tìm thấy đơn hàng nào'
                : _selectedStatus != null
                    ? 'Không có đơn hàng nào với trạng thái này'
                    : 'Chưa có đơn hàng nào',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tạo đơn hàng mới hoặc thử tìm kiếm khác',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => context.push('/orders/create'),
            icon: const Icon(Icons.add),
            label: const Text('Tạo đơn hàng mới'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateOrderStatus(OrderModel order, String newStatus) async {
    try {
      final success = await _orderService.updateOrderStatus(order.id, newStatus);
      
      if (success) {
        setState(() {
          final index = _orders.indexWhere((o) => o.id == order.id);
          if (index != -1) {
            _orders[index] = order.copyWith(
              status: newStatus,
              updatedAt: DateTime.now(),
            );
          }
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cập nhật trạng thái thành công')),
        );
        
        // Reload statistics
        _loadStatistics();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cập nhật trạng thái thất bại')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi: $e')),
      );
    }
  }


}
