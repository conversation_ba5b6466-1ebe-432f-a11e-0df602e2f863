import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../shared/themes/app_theme.dart';
import '../../../core/constants/app_constants.dart';
import '../services/order_service.dart';
import '../models/order_model.dart';
import '../widgets/order_status_timeline.dart';
import '../widgets/signature_pad.dart';
import '../widgets/photo_capture.dart';

class OrderDetailScreen extends StatefulWidget {
  final String orderId;

  const OrderDetailScreen({super.key, required this.orderId});

  @override
  State<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen> {
  final OrderService _orderService = OrderService();
  OrderModel? _order;
  bool _isLoading = true;
  bool _isUpdating = false;
  String? _signatureData;
  String? _photoPath;

  @override
  void initState() {
    super.initState();
    _loadOrderDetail();
  }

  Future<void> _loadOrderDetail() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _orderService.initialize();
      final order = await _orderService.getOrderById(widget.orderId);
      
      setState(() {
        _order = order;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi tải chi tiết đơn hàng: $e')),
        );
      }
    }
  }

  Future<void> _updateOrderStatus(String newStatus) async {
    if (_order == null) return;

    // Check if confirmation is required for certain status changes
    if (_requiresConfirmation(newStatus)) {
      await _showConfirmationDialog(newStatus);
      return;
    }

    await _performStatusUpdate(newStatus);
  }

  bool _requiresConfirmation(String status) {
    return status == AppConstants.orderDelivered ||
           status == AppConstants.orderCancelled;
  }

  Future<void> _showConfirmationDialog(String newStatus) async {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getConfirmationTitle(newStatus)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(_getConfirmationMessage(newStatus)),
            const SizedBox(height: 16),
            if (newStatus == AppConstants.orderDelivered) ...[
              Text(
                'Vui lòng chọn phương thức xác nhận:',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _showSignaturePad(newStatus);
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('Chữ ký'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _showPhotoCapture(newStatus);
                      },
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('Chụp ảnh'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        actions: [
          if (newStatus == AppConstants.orderCancelled) ...[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performStatusUpdate(newStatus);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.error,
                foregroundColor: theme.colorScheme.onError,
              ),
              child: const Text('Xác nhận hủy'),
            ),
          ] else ...[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Đóng'),
            ),
          ],
        ],
      ),
    );
  }

  String _getConfirmationTitle(String status) {
    switch (status) {
      case AppConstants.orderDelivered:
        return 'Xác nhận giao hàng';
      case AppConstants.orderCancelled:
        return 'Xác nhận hủy đơn';
      default:
        return 'Xác nhận';
    }
  }

  String _getConfirmationMessage(String status) {
    switch (status) {
      case AppConstants.orderDelivered:
        return 'Đơn hàng đã được giao thành công cho khách hàng?';
      case AppConstants.orderCancelled:
        return 'Bạn có chắc chắn muốn hủy đơn hàng này không?';
      default:
        return 'Bạn có chắc chắn muốn thay đổi trạng thái không?';
    }
  }

  Future<void> _showSignaturePad(String newStatus) async {
    showDialog(
      context: context,
      builder: (context) => SignaturePad(
        title: 'Chữ ký xác nhận giao hàng',
        onSignatureComplete: (signature) {
          setState(() {
            _signatureData = signature;
          });
          _performStatusUpdate(newStatus);
        },
      ),
    );
  }

  Future<void> _showPhotoCapture(String newStatus) async {
    showDialog(
      context: context,
      builder: (context) => PhotoCapture(
        title: 'Chụp ảnh xác nhận giao hàng',
        onPhotoTaken: (photoPath) {
          setState(() {
            _photoPath = photoPath;
          });
          _performStatusUpdate(newStatus);
        },
      ),
    );
  }

  Future<void> _performStatusUpdate(String newStatus) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await _orderService.updateOrderStatus(_order!.id, newStatus);

      if (success) {
        setState(() {
          _order = _order!.copyWith(
            status: newStatus,
            updatedAt: DateTime.now(),
          );
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_getSuccessMessage(newStatus)),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cập nhật trạng thái thất bại')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi: $e')),
      );
    } finally {
      setState(() {
        _isUpdating = false;
      });
    }
  }

  String _getSuccessMessage(String status) {
    switch (status) {
      case AppConstants.orderDelivered:
        return 'Đã xác nhận giao hàng thành công';
      case AppConstants.orderCancelled:
        return 'Đã hủy đơn hàng';
      default:
        return 'Cập nhật trạng thái thành công';
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'print':
        _printOrder();
        break;
      case 'share':
        _shareOrder();
        break;
      default:
        // Handle status updates
        _updateOrderStatus(action);
        break;
    }
  }

  void _printOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tính năng in đơn hàng đang phát triển'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareOrder() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Chia sẻ đơn hàng: ${_order?.orderNumber}'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Scaffold(
      appBar: AppBar(
        title: Text(_order?.orderNumber ?? 'Chi tiết đơn hàng'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
        actions: [
          if (_order != null)
            PopupMenuButton<String>(
              onSelected: _handleMenuAction,
              itemBuilder: (context) => _buildStatusMenuItems(),
              child: _isUpdating
                  ? const Padding(
                      padding: EdgeInsets.all(16),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : const Icon(Icons.more_vert),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _order == null
              ? _buildErrorState()
              : RefreshIndicator(
                  onRefresh: _loadOrderDetail,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildOrderHeader(),
                        const SizedBox(height: 16),
                        _buildStatusTimeline(),
                        const SizedBox(height: 16),
                        _buildCustomerInfo(),
                        const SizedBox(height: 16),
                        _buildAddressInfo(),
                        const SizedBox(height: 16),
                        _buildItemsList(),
                        const SizedBox(height: 16),
                        _buildOrderSummary(),
                        if (_order!.notes?.isNotEmpty == true) ...[
                          const SizedBox(height: 16),
                          _buildNotes(),
                        ],
                        if (_signatureData != null || _photoPath != null) ...[
                          const SizedBox(height: 16),
                          _buildConfirmationData(),
                        ],
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Không tìm thấy đơn hàng',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Đơn hàng có ID "${widget.orderId}" không tồn tại.',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Quay lại'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderHeader() {
    final theme = Theme.of(context);
    final statusColor = AppTheme.getOrderStatusColor(_order!.status);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _order!.orderNumber,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Tạo lúc: ${DateFormat('dd/MM/yyyy HH:mm').format(_order!.createdAt)}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: statusColor.withOpacity(0.4),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: statusColor.withOpacity(0.1),
                        blurRadius: 6,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(3),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          AppTheme.getOrderStatusIcon(_order!.status),
                          size: 16,
                          color: statusColor,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _getStatusDisplayName(_order!.status),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.w700,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_order!.qrCode != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.qr_code,
                      size: 16,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'QR Code: ${_order!.qrCode}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusTimeline() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trạng thái đơn hàng',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            OrderStatusTimeline(order: _order!),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin khách hàng',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.person, 'Tên khách hàng', _order!.customerName),
            if (_order!.customerPhone?.isNotEmpty == true)
              _buildInfoRow(Icons.phone, 'Số điện thoại', _order!.customerPhone!),
            if (_order!.driverName?.isNotEmpty == true)
              _buildInfoRow(Icons.local_shipping, 'Tài xế', _order!.driverName!),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressInfo() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Địa chỉ giao hàng',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildAddressItem('Điểm lấy hàng', _order!.pickupAddress, Icons.location_on),
            const SizedBox(height: 8),
            _buildAddressItem('Điểm giao hàng', _order!.deliveryAddress, Icons.location_on),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Danh sách sản phẩm',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...(_order!.items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.inventory_2,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (item.description?.isNotEmpty == true) ...[
                          const SizedBox(height: 2),
                          Text(
                            item.description!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'SL: ${item.quantity}',
                              style: theme.textTheme.bodySmall,
                            ),
                            const SizedBox(width: 16),
                            Text(
                              currencyFormat.format(item.unitPrice),
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Text(
                    currencyFormat.format(item.totalPrice),
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ))),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummary() {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tổng kết đơn hàng',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tổng tiền hàng:',
                  style: theme.textTheme.bodyLarge,
                ),
                Text(
                  currencyFormat.format(_order!.totalAmount),
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tổng thanh toán:',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  currencyFormat.format(_order!.totalAmount),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotes() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ghi chú',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _order!.notes!,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressItem(String title, AddressModel address, IconData icon) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  address.fullAddress,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildStatusMenuItems() {
    if (_order == null) return [];

    final currentStatus = _order!.status;
    final menuItems = <PopupMenuEntry<String>>[];

    if (currentStatus == AppConstants.orderPending) {
      menuItems.add(
        PopupMenuItem(
          value: AppConstants.orderConfirmed,
          child: Row(
            children: [
              Icon(Icons.check_circle_outline, color: AppTheme.confirmedColor),
              const SizedBox(width: 8),
              const Text('Xác nhận đơn hàng'),
            ],
          ),
        ),
      );
    }

    if (currentStatus == AppConstants.orderConfirmed) {
      menuItems.add(
        PopupMenuItem(
          value: AppConstants.orderInTransit,
          child: Row(
            children: [
              Icon(Icons.local_shipping, color: AppTheme.inTransitColor),
              const SizedBox(width: 8),
              const Text('Bắt đầu giao hàng'),
            ],
          ),
        ),
      );
    }

    if (currentStatus == AppConstants.orderInTransit) {
      menuItems.add(
        PopupMenuItem(
          value: AppConstants.orderDelivered,
          child: Row(
            children: [
              Icon(Icons.check_circle, color: AppTheme.deliveredColor),
              const SizedBox(width: 8),
              const Text('Giao hàng thành công'),
            ],
          ),
        ),
      );
    }

    // Add divider before cancel option
    if (currentStatus != AppConstants.orderCancelled &&
        currentStatus != AppConstants.orderDelivered) {
      if (menuItems.isNotEmpty) {
        menuItems.add(const PopupMenuDivider());
      }
      menuItems.add(
        PopupMenuItem(
          value: AppConstants.orderCancelled,
          child: Row(
            children: [
              Icon(Icons.cancel, color: AppTheme.cancelledColor),
              const SizedBox(width: 8),
              const Text('Hủy đơn hàng'),
            ],
          ),
        ),
      );
    }

    // Add additional actions
    if (menuItems.isNotEmpty) {
      menuItems.add(const PopupMenuDivider());
    }

    menuItems.add(
      PopupMenuItem(
        value: 'print',
        child: Row(
          children: [
            Icon(Icons.print, color: Colors.blue),
            const SizedBox(width: 8),
            const Text('In đơn hàng'),
          ],
        ),
      ),
    );

    menuItems.add(
      PopupMenuItem(
        value: 'share',
        child: Row(
          children: [
            Icon(Icons.share, color: Colors.green),
            const SizedBox(width: 8),
            const Text('Chia sẻ'),
          ],
        ),
      ),
    );

    return menuItems;
  }

  Widget _buildConfirmationData() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Xác nhận giao hàng',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            if (_signatureData != null) ...[
              _buildConfirmationItem(
                'Chữ ký xác nhận',
                Icons.edit,
                'Đã có chữ ký xác nhận',
                onTap: () => _showSignaturePreview(),
              ),
            ],

            if (_photoPath != null) ...[
              if (_signatureData != null) const SizedBox(height: 12),
              _buildConfirmationItem(
                'Ảnh xác nhận',
                Icons.camera_alt,
                'Đã có ảnh xác nhận',
                onTap: () => _showPhotoPreview(),
              ),
            ],

            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.verified,
                    color: Colors.green,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Giao hàng đã được xác nhận vào ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfirmationItem(
    String title,
    IconData icon,
    String subtitle,
    {VoidCallback? onTap}
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: theme.colorScheme.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
              ),
          ],
        ),
      ),
    );
  }

  void _showSignaturePreview() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Chữ ký xác nhận',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                width: 300,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text('Signature Preview\n(Mock)'),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Đóng'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPhotoPreview() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Ảnh xác nhận',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                width: 300,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text('Photo Preview\n(Mock)'),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Đóng'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case AppConstants.orderPending:
        return 'Chờ xử lý';
      case AppConstants.orderConfirmed:
        return 'Đã xác nhận';
      case AppConstants.orderInTransit:
        return 'Đang giao';
      case AppConstants.orderDelivered:
        return 'Đã giao';
      case AppConstants.orderCancelled:
        return 'Đã hủy';
      default:
        return 'Không xác định';
    }
  }
}
