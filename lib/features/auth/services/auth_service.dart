import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';
import '../models/auth_models.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final Dio _dio = Dio();
  UserModel? _currentUser;
  String? _token;

  UserModel? get currentUser => _currentUser;
  String? get token => _token;
  bool get isAuthenticated => _token != null && _currentUser != null;

  Future<void> initialize() async {
    _dio.options.baseUrl = AppConstants.baseUrl + AppConstants.apiVersion;
    _dio.options.connectTimeout = AppConstants.requestTimeout;
    _dio.options.receiveTimeout = AppConstants.requestTimeout;

    // Add interceptor for token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_token != null) {
          options.headers['Authorization'] = 'Bearer $_token';
        }
        handler.next(options);
      },
      onError: (error, handler) {
        if (error.response?.statusCode == 401) {
          // Token expired, logout user
          logout();
        }
        handler.next(error);
      },
    ));

    // Load saved user data
    await _loadSavedAuth();
  }

  Future<AuthResult> login(String email, String password) async {
    try {
      // Mock implementation - replace with real API later
      await Future.delayed(Duration(seconds: 1)); // Simulate network delay

      // Mock users for testing
      final mockUsers = {
        '<EMAIL>': {
          'password': '123456',
          'user': UserModel(
            id: '1',
            email: '<EMAIL>',
            name: 'Admin User',
            role: AppConstants.roleAdmin,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        },
        '<EMAIL>': {
          'password': '123456',
          'user': UserModel(
            id: '2',
            email: '<EMAIL>',
            name: 'Driver User',
            role: AppConstants.roleDriver,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        },
        '<EMAIL>': {
          'password': '123456',
          'user': UserModel(
            id: '3',
            email: '<EMAIL>',
            name: 'Customer User',
            role: AppConstants.roleCustomer,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        },
        '<EMAIL>': {
          'password': '123456',
          'user': UserModel(
            id: '4',
            email: '<EMAIL>',
            name: 'Warehouse User',
            role: AppConstants.roleWarehouse,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        },
      };

      // Check if user exists and password is correct
      final mockUser = mockUsers[email.toLowerCase()];
      if (mockUser != null && mockUser['password'] == password) {
        _token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        _currentUser = mockUser['user'] as UserModel;

        await _saveAuth();

        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Email hoặc mật khẩu không đúng');
      }

      /* Real API implementation - uncomment when you have backend:
      final hashedPassword = _hashPassword(password);

      final response = await _dio.post('/auth/login', data: {
        'email': email,
        'password': hashedPassword,
      });

      if (response.statusCode == 200) {
        final data = response.data;
        _token = data['token'];
        _currentUser = UserModel.fromJson(data['user']);

        await _saveAuth();

        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Login failed');
      }
      */
    } on DioException catch (e) {
      return AuthResult.failure(_handleDioError(e));
    } catch (e) {
      return AuthResult.failure('Có lỗi xảy ra: $e');
    }
  }

  Future<AuthResult> register(RegisterRequest request) async {
    try {
      final hashedPassword = _hashPassword(request.password);
      
      final response = await _dio.post('/auth/register', data: {
        'email': request.email,
        'password': hashedPassword,
        'name': request.name,
        'phone': request.phone,
        'role': request.role,
      });

      if (response.statusCode == 201) {
        final data = response.data;
        _token = data['token'];
        _currentUser = UserModel.fromJson(data['user']);
        
        await _saveAuth();
        
        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Registration failed');
      }
    } on DioException catch (e) {
      return AuthResult.failure(_handleDioError(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  Future<void> logout() async {
    try {
      if (_token != null) {
        await _dio.post('/auth/logout');
      }
    } catch (e) {
      // Ignore logout errors
    } finally {
      _token = null;
      _currentUser = null;
      await _clearAuth();
    }
  }

  Future<AuthResult> refreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final refreshToken = prefs.getString(AppConstants.refreshTokenKey);
      
      if (refreshToken == null) {
        return AuthResult.failure('No refresh token available');
      }

      final response = await _dio.post('/auth/refresh', data: {
        'refresh_token': refreshToken,
      });

      if (response.statusCode == 200) {
        final data = response.data;
        _token = data['token'];
        _currentUser = UserModel.fromJson(data['user']);
        
        await _saveAuth();
        
        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Token refresh failed');
      }
    } on DioException catch (e) {
      return AuthResult.failure(_handleDioError(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  Future<AuthResult> updateProfile(UpdateProfileRequest request) async {
    try {
      final response = await _dio.put('/auth/profile', data: request.toJson());

      if (response.statusCode == 200) {
        _currentUser = UserModel.fromJson(response.data['user']);
        await _saveAuth();
        
        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Profile update failed');
      }
    } on DioException catch (e) {
      return AuthResult.failure(_handleDioError(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      final hashedCurrentPassword = _hashPassword(currentPassword);
      final hashedNewPassword = _hashPassword(newPassword);
      
      final response = await _dio.put('/auth/change-password', data: {
        'current_password': hashedCurrentPassword,
        'new_password': hashedNewPassword,
      });

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<ForgotPasswordResult> forgotPassword(String email) async {
    try {
      print('🔥 DEBUG: AuthService.forgotPassword called with email: $email');
      // Mock implementation - replace with real API
      await Future.delayed(Duration(seconds: 2));

      // Simulate email validation
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
        print('❌ DEBUG: Email validation failed');
        return ForgotPasswordResult.failure('Email không hợp lệ');
      }

      // Simulate sending OTP email
      print('✅ DEBUG: Sending OTP to email: $email');
      return ForgotPasswordResult.success('Mã OTP đã được gửi đến email của bạn');

      /* Real API implementation:
      final response = await _dio.post('/auth/forgot-password', data: {
        'email': email,
      });

      if (response.statusCode == 200) {
        return ForgotPasswordResult.success(
          response.data['message'] ?? 'OTP sent successfully'
        );
      } else {
        return ForgotPasswordResult.failure('Failed to send OTP');
      }
      */
    } on DioException catch (e) {
      return ForgotPasswordResult.failure(_handleDioError(e));
    } catch (e) {
      return ForgotPasswordResult.failure('Có lỗi xảy ra, vui lòng thử lại');
    }
  }

  Future<ForgotPasswordResult> verifyOtp(String email, String otp) async {
    try {
      // Mock implementation - replace with real API
      await Future.delayed(Duration(seconds: 1));

      // Simulate OTP verification (for demo, accept "123456")
      if (otp == '123456') {
        return ForgotPasswordResult.success('OTP xác thực thành công');
      } else {
        return ForgotPasswordResult.failure('Mã OTP không đúng');
      }

      /* Real API implementation:
      final response = await _dio.post('/auth/verify-otp', data: {
        'email': email,
        'otp': otp,
      });

      if (response.statusCode == 200) {
        return ForgotPasswordResult.success('OTP verified successfully');
      } else {
        return ForgotPasswordResult.failure('Invalid OTP');
      }
      */
    } on DioException catch (e) {
      return ForgotPasswordResult.failure(_handleDioError(e));
    } catch (e) {
      return ForgotPasswordResult.failure('Có lỗi xảy ra, vui lòng thử lại');
    }
  }

  Future<ForgotPasswordResult> resetPassword(
    String email,
    String otp,
    String newPassword
  ) async {
    try {
      // Mock implementation - replace with real API
      await Future.delayed(Duration(seconds: 2));

      // Simulate password reset
      if (otp == '123456' && newPassword.length >= 6) {
        return ForgotPasswordResult.success('Mật khẩu đã được đổi thành công');
      } else {
        return ForgotPasswordResult.failure('Không thể đổi mật khẩu');
      }

      /* Real API implementation:
      final hashedPassword = _hashPassword(newPassword);
      final response = await _dio.post('/auth/reset-password', data: {
        'email': email,
        'otp': otp,
        'new_password': hashedPassword,
      });

      if (response.statusCode == 200) {
        return ForgotPasswordResult.success('Password reset successfully');
      } else {
        return ForgotPasswordResult.failure('Failed to reset password');
      }
      */
    } on DioException catch (e) {
      return ForgotPasswordResult.failure(_handleDioError(e));
    } catch (e) {
      return ForgotPasswordResult.failure('Có lỗi xảy ra, vui lòng thử lại');
    }
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Server error';
        return '$message (Status: $statusCode)';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.unknown:
        return 'Network error. Please check your internet connection.';
      default:
        return 'An unexpected error occurred';
    }
  }

  Future<void> _saveAuth() async {
    final prefs = await SharedPreferences.getInstance();
    if (_token != null) {
      await prefs.setString(AppConstants.tokenKey, _token!);
    }
    if (_currentUser != null) {
      await prefs.setString(AppConstants.userKey, jsonEncode(_currentUser!.toJson()));
    }
  }

  Future<void> _loadSavedAuth() async {
    final prefs = await SharedPreferences.getInstance();
    _token = prefs.getString(AppConstants.tokenKey);
    
    final userJson = prefs.getString(AppConstants.userKey);
    if (userJson != null) {
      try {
        _currentUser = UserModel.fromJson(jsonDecode(userJson));
      } catch (e) {
        // Invalid user data, clear it
        await _clearAuth();
      }
    }
  }

  Future<void> _clearAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.tokenKey);
    await prefs.remove(AppConstants.userKey);
    await prefs.remove(AppConstants.refreshTokenKey);
  }
}
