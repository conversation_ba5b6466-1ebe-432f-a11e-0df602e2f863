import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../features/auth/services/auth_service.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final AuthService _authService = AuthService();
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  void _loadUserInfo() {
    setState(() {
      _currentUser = _authService.currentUser;
    });
  }

  Future<void> _logout() async {
    final confirmed = await _showLogoutConfirmDialog();
    if (confirmed == true) {
      await _authService.logout();
      if (mounted) {
        context.go('/login');
      }
    }
  }

  Future<bool?> _showLogoutConfirmDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đăng xuất'),
        content: const Text('Bạn có chắc chắn muốn đăng xuất không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Đăng xuất'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Hồ sơ cá nhân'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // User Info Card
            _buildUserInfoCard(),
            const SizedBox(height: 24),
            
            // Menu Items
            _buildMenuItem(
              icon: Icons.person_outline,
              title: 'Thông tin cá nhân',
              subtitle: 'Xem và chỉnh sửa thông tin',
              onTap: () {
                context.push('/profile/edit');
              },
            ),
            
            _buildMenuItem(
              icon: Icons.lock_outline,
              title: 'Đổi mật khẩu',
              subtitle: 'Thay đổi mật khẩu đăng nhập',
              onTap: () {
                // TODO: Navigate to change password
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Tính năng đang phát triển')),
                );
              },
            ),
            
            _buildMenuItem(
              icon: Icons.notifications,
              title: 'Thông báo',
              subtitle: 'Cài đặt thông báo',
              onTap: () {
                // TODO: Navigate to notification settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Tính năng đang phát triển')),
                );
              },
            ),
            
            _buildMenuItem(
              icon: Icons.security,
              title: 'Bảo mật',
              subtitle: 'Cài đặt bảo mật tài khoản',
              onTap: () {
                // TODO: Navigate to security settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Tính năng đang phát triển')),
                );
              },
            ),
            
            _buildMenuItem(
              icon: Icons.help_outline,
              title: 'Trợ giúp',
              subtitle: 'Hướng dẫn sử dụng và hỗ trợ',
              onTap: () {
                _showHelpDialog();
              },
            ),
            
            _buildMenuItem(
              icon: Icons.info_outline,
              title: 'Về ứng dụng',
              subtitle: 'Thông tin phiên bản và nhà phát triển',
              onTap: () {
                _showAboutDialog();
              },
            ),
            
            const SizedBox(height: 24),
            
            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _logout,
                icon: const Icon(Icons.logout),
                label: const Text('Đăng xuất'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.error,
                  foregroundColor: theme.colorScheme.onError,
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // App Version
            Text(
              'Flutter Logistics v1.0.0',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Avatar
            CircleAvatar(
              radius: 40,
              backgroundColor: theme.colorScheme.primary,
              child: Text(
                _currentUser?.name.substring(0, 1).toUpperCase() ?? 'U',
                style: TextStyle(
                  color: theme.colorScheme.onPrimary,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Name
            Text(
              _currentUser?.name ?? 'Người dùng',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            
            // Email
            Text(
              _currentUser?.email ?? '',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            
            // Role Badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.3),
                ),
              ),
              child: Text(
                _getRoleDisplayName(_currentUser?.role ?? ''),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
          ),
        ),
        title: Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case AppConstants.roleAdmin:
        return 'Quản trị viên';
      case AppConstants.roleDriver:
        return 'Tài xế';
      case AppConstants.roleCustomer:
        return 'Khách hàng';
      case AppConstants.roleWarehouse:
        return 'Nhân viên kho';
      default:
        return 'Người dùng';
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Trợ giúp'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Hướng dẫn sử dụng:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Dashboard: Xem tổng quan và thống kê'),
              Text('• Đơn hàng: Quản lý và theo dõi đơn hàng'),
              Text('• Quét mã: Sử dụng camera để quét QR code'),
              Text('• Theo dõi: Xem trạng thái giao hàng'),
              Text('• Hồ sơ: Quản lý thông tin cá nhân'),
              SizedBox(height: 16),
              Text(
                'Liên hệ hỗ trợ:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('Email: <EMAIL>'),
              Text('Hotline: 1900-xxxx'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'Flutter Logistics',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        Icons.local_shipping,
        size: 48,
        color: Theme.of(context).colorScheme.primary,
      ),
      children: [
        const Text(
          'Ứng dụng quản lý logistics được phát triển bằng Flutter. '
          'Hỗ trợ quản lý đơn hàng, theo dõi vận chuyển và quét mã QR.',
        ),
        const SizedBox(height: 16),
        const Text(
          'Phát triển bởi: Flutter Team\n'
          'Phiên bản: 1.0.0\n'
          'Ngày phát hành: 2024',
        ),
      ],
    );
  }
}
