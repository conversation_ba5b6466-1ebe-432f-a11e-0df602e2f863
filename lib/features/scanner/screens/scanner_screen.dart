import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:go_router/go_router.dart';
import '../services/scanner_service.dart';
import '../models/scan_result_model.dart';
import '../widgets/scanner_overlay.dart';

class ScannerScreen extends StatefulWidget {
  const ScannerScreen({super.key});

  @override
  State<ScannerScreen> createState() => _ScannerScreenState();
}

class _ScannerScreenState extends State<ScannerScreen>
    with WidgetsBindingObserver {
  final ScannerService _scannerService = ScannerService();
  bool _isFlashOn = false;
  bool _isFrontCamera = false;
  bool _hasPermission = false;
  String? _lastScannedCode;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkPermissionAndStart();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scannerService.stopScanning();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        if (_hasPermission) {
          _scannerService.startScanning();
        }
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _scannerService.stopScanning();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        break;
    }
  }

  Future<void> _checkPermissionAndStart() async {
    final hasPermission = await _scannerService.checkCameraPermission();
    
    if (!hasPermission) {
      final granted = await _scannerService.requestCameraPermission();
      if (!granted) {
        if (mounted) {
          _showPermissionDialog();
        }
        return;
      }
    }

    setState(() {
      _hasPermission = true;
    });

    await _scannerService.startScanning();
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Quyền truy cập camera'),
        content: const Text(
          'Ứng dụng cần quyền truy cập camera để quét mã QR. '
          'Vui lòng cấp quyền trong cài đặt.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.pop();
            },
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _checkPermissionAndStart();
            },
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  void _onDetect(BarcodeCapture capture) {
    final scanResult = _scannerService.parseScanResult(capture);
    
    // Prevent duplicate scans
    if (_lastScannedCode == scanResult.rawValue) {
      return;
    }
    
    _lastScannedCode = scanResult.rawValue;
    
    // Vibrate on successful scan
    // HapticFeedback.mediumImpact();
    
    _handleScanResult(scanResult);
  }

  void _handleScanResult(ScanResultModel result) {
    _scannerService.stopScanning();
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ScanResultBottomSheet(
        result: result,
        onAction: (action) {
          Navigator.of(context).pop();
          _handleScanAction(action, result);
        },
        onClose: () {
          Navigator.of(context).pop();
          _resumeScanning();
        },
      ),
    );
  }

  void _handleScanAction(String action, ScanResultModel result) {
    switch (action) {
      case 'view_order':
        context.push('/orders/${result.value}');
        break;
      case 'update_status':
        context.push('/orders/${result.value}/update-status');
        break;
      case 'view_package':
        context.push('/packages/${result.value}');
        break;
      case 'open_url':
        // Open URL in browser
        break;
      case 'copy':
        // Copy to clipboard
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Đã sao chép vào clipboard')),
        );
        _resumeScanning();
        break;
      default:
        _resumeScanning();
    }
  }

  void _resumeScanning() {
    _lastScannedCode = null;
    _scannerService.startScanning();
  }

  Future<void> _toggleFlash() async {
    await _scannerService.toggleTorch();
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
  }

  Future<void> _switchCamera() async {
    await _scannerService.switchCamera();
    setState(() {
      _isFrontCamera = !_isFrontCamera;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasPermission) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Quét mã QR'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Quét mã QR'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _toggleFlash,
            icon: Icon(_isFlashOn ? Icons.flash_on : Icons.flash_off),
          ),
          IconButton(
            onPressed: _switchCamera,
            icon: const Icon(Icons.flip_camera_ios),
          ),
        ],
      ),
      body: Stack(
        children: [
          MobileScanner(
            controller: _scannerService.controller,
            onDetect: _onDetect,
          ),
          const ScannerOverlay(),
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Đưa camera về phía mã QR để quét',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildActionButton(
                        icon: _isFlashOn ? Icons.flash_on : Icons.flash_off,
                        label: 'Đèn flash',
                        onTap: _toggleFlash,
                      ),
                      _buildActionButton(
                        icon: Icons.flip_camera_ios,
                        label: 'Đổi camera',
                        onTap: _switchCamera,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ScanResultBottomSheet extends StatelessWidget {
  final ScanResultModel result;
  final Function(String) onAction;
  final VoidCallback onClose;

  const _ScanResultBottomSheet({
    required this.result,
    required this.onAction,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getTypeIcon(result.type),
                      color: theme.colorScheme.primary,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Quét thành công',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            result.typeDisplayName,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: onClose,
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nội dung:',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        result.value,
                        style: theme.textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Định dạng: ${result.formatDisplayName}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                ..._buildActionButtons(context, result),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTypeIcon(ScanType type) {
    switch (type) {
      case ScanType.order:
        return Icons.shopping_bag;
      case ScanType.package:
        return Icons.inventory_2;
      case ScanType.location:
        return Icons.location_on;
      case ScanType.url:
        return Icons.link;
      case ScanType.data:
        return Icons.data_object;
      case ScanType.unknown:
        return Icons.help_outline;
    }
  }

  List<Widget> _buildActionButtons(BuildContext context, ScanResultModel result) {
    final buttons = <Widget>[];
    
    switch (result.type) {
      case ScanType.order:
        buttons.addAll([
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => onAction('view_order'),
              icon: const Icon(Icons.visibility),
              label: const Text('Xem đơn hàng'),
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => onAction('update_status'),
              icon: const Icon(Icons.update),
              label: const Text('Cập nhật trạng thái'),
            ),
          ),
        ]);
        break;
      case ScanType.package:
        buttons.add(
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => onAction('view_package'),
              icon: const Icon(Icons.inventory_2),
              label: const Text('Xem gói hàng'),
            ),
          ),
        );
        break;
      case ScanType.url:
        buttons.add(
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => onAction('open_url'),
              icon: const Icon(Icons.open_in_browser),
              label: const Text('Mở liên kết'),
            ),
          ),
        );
        break;
      default:
        buttons.add(
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => onAction('copy'),
              icon: const Icon(Icons.copy),
              label: const Text('Sao chép'),
            ),
          ),
        );
    }
    
    return buttons;
  }
}
