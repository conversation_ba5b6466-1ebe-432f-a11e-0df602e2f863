import 'package:mobile_scanner/mobile_scanner.dart';

enum ScanType {
  order,
  package,
  location,
  url,
  data,
  unknown,
}

class ScanResultModel {
  final ScanType type;
  final String value;
  final String rawValue;
  final BarcodeFormat format;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  ScanResultModel({
    required this.type,
    required this.value,
    required this.rawValue,
    required this.format,
    required this.timestamp,
    this.metadata,
  });

  factory ScanResultModel.fromJson(Map<String, dynamic> json) {
    return ScanResultModel(
      type: ScanType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => ScanType.unknown,
      ),
      value: json['value'] as String,
      rawValue: json['raw_value'] as String,
      format: BarcodeFormat.values.firstWhere(
        (e) => e.toString() == json['format'],
        orElse: () => BarcodeFormat.unknown,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'value': value,
      'raw_value': rawValue,
      'format': format.toString(),
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  String get typeDisplayName {
    switch (type) {
      case ScanType.order:
        return 'Đơn hàng';
      case ScanType.package:
        return 'Gói hàng';
      case ScanType.location:
        return 'Vị trí';
      case ScanType.url:
        return 'Liên kết';
      case ScanType.data:
        return 'Dữ liệu';
      case ScanType.unknown:
        return 'Không xác định';
    }
  }

  String get formatDisplayName {
    switch (format) {
      case BarcodeFormat.qrCode:
        return 'QR Code';
      case BarcodeFormat.code128:
        return 'Code 128';
      case BarcodeFormat.code39:
        return 'Code 39';
      case BarcodeFormat.code93:
        return 'Code 93';
      case BarcodeFormat.ean13:
        return 'EAN-13';
      case BarcodeFormat.ean8:
        return 'EAN-8';
      case BarcodeFormat.upcA:
        return 'UPC-A';
      case BarcodeFormat.upcE:
        return 'UPC-E';
      case BarcodeFormat.dataMatrix:
        return 'Data Matrix';
      case BarcodeFormat.pdf417:
        return 'PDF417';
      case BarcodeFormat.aztec:
        return 'Aztec';
      case BarcodeFormat.codabar:
        return 'Codabar';
      case BarcodeFormat.itf:
        return 'ITF';
      default:
        return 'Không xác định';
    }
  }

  ScanResultModel copyWith({
    ScanType? type,
    String? value,
    String? rawValue,
    BarcodeFormat? format,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return ScanResultModel(
      type: type ?? this.type,
      value: value ?? this.value,
      rawValue: rawValue ?? this.rawValue,
      format: format ?? this.format,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'ScanResultModel(type: $type, value: $value, format: $format)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScanResultModel &&
        other.type == type &&
        other.value == value &&
        other.rawValue == rawValue &&
        other.format == format;
  }

  @override
  int get hashCode {
    return type.hashCode ^
        value.hashCode ^
        rawValue.hashCode ^
        format.hashCode;
  }
}
