import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/scan_result_model.dart';

class ScannerService {
  static final ScannerService _instance = ScannerService._internal();
  factory ScannerService() => _instance;
  ScannerService._internal();

  MobileScannerController? _controller;
  bool _isScanning = false;

  MobileScannerController get controller {
    _controller ??= MobileScannerController(
      detectionSpeed: DetectionSpeed.noDuplicates,
      facing: CameraFacing.back,
      torchEnabled: false,
    );
    return _controller!;
  }

  bool get isScanning => _isScanning;

  Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status == PermissionStatus.granted;
  }

  Future<bool> checkCameraPermission() async {
    final status = await Permission.camera.status;
    return status == PermissionStatus.granted;
  }

  Future<void> startScanning() async {
    if (!_isScanning) {
      _isScanning = true;
      await controller.start();
    }
  }

  Future<void> stopScanning() async {
    if (_isScanning) {
      _isScanning = false;
      await controller.stop();
    }
  }

  Future<void> toggleTorch() async {
    await controller.toggleTorch();
  }

  Future<void> switchCamera() async {
    await controller.switchCamera();
  }

  void dispose() {
    _controller?.dispose();
    _controller = null;
    _isScanning = false;
  }

  ScanResultModel parseScanResult(BarcodeCapture capture) {
    final barcode = capture.barcodes.first;
    final rawValue = barcode.rawValue ?? '';
    
    // Parse different QR code formats
    if (rawValue.startsWith('ORDER:')) {
      return ScanResultModel(
        type: ScanType.order,
        value: rawValue.substring(6),
        rawValue: rawValue,
        format: barcode.format,
        timestamp: DateTime.now(),
      );
    } else if (rawValue.startsWith('PACKAGE:')) {
      return ScanResultModel(
        type: ScanType.package,
        value: rawValue.substring(8),
        rawValue: rawValue,
        format: barcode.format,
        timestamp: DateTime.now(),
      );
    } else if (rawValue.startsWith('LOCATION:')) {
      return ScanResultModel(
        type: ScanType.location,
        value: rawValue.substring(9),
        rawValue: rawValue,
        format: barcode.format,
        timestamp: DateTime.now(),
      );
    } else if (rawValue.startsWith('http://') || rawValue.startsWith('https://')) {
      return ScanResultModel(
        type: ScanType.url,
        value: rawValue,
        rawValue: rawValue,
        format: barcode.format,
        timestamp: DateTime.now(),
      );
    } else {
      // Try to parse as JSON for complex data
      try {
        return ScanResultModel(
          type: ScanType.data,
          value: rawValue,
          rawValue: rawValue,
          format: barcode.format,
          timestamp: DateTime.now(),
        );
      } catch (e) {
        return ScanResultModel(
          type: ScanType.unknown,
          value: rawValue,
          rawValue: rawValue,
          format: barcode.format,
          timestamp: DateTime.now(),
        );
      }
    }
  }

  String formatBarcodeFormat(BarcodeFormat format) {
    switch (format) {
      case BarcodeFormat.qrCode:
        return 'QR Code';
      case BarcodeFormat.code128:
        return 'Code 128';
      case BarcodeFormat.code39:
        return 'Code 39';
      case BarcodeFormat.code93:
        return 'Code 93';
      case BarcodeFormat.ean13:
        return 'EAN-13';
      case BarcodeFormat.ean8:
        return 'EAN-8';
      case BarcodeFormat.upcA:
        return 'UPC-A';
      case BarcodeFormat.upcE:
        return 'UPC-E';
      case BarcodeFormat.dataMatrix:
        return 'Data Matrix';
      case BarcodeFormat.pdf417:
        return 'PDF417';
      case BarcodeFormat.aztec:
        return 'Aztec';
      case BarcodeFormat.codabar:
        return 'Codabar';
      case BarcodeFormat.itf:
        return 'ITF';
      default:
        return 'Unknown';
    }
  }

  bool isOrderCode(String value) {
    return value.startsWith('ORDER:') || 
           RegExp(r'^[A-Z0-9]{6,12}$').hasMatch(value);
  }

  bool isPackageCode(String value) {
    return value.startsWith('PACKAGE:') || 
           RegExp(r'^PKG[A-Z0-9]{6,10}$').hasMatch(value);
  }

  bool isLocationCode(String value) {
    return value.startsWith('LOCATION:') || 
           RegExp(r'^LOC[A-Z0-9]{4,8}$').hasMatch(value);
  }
}
