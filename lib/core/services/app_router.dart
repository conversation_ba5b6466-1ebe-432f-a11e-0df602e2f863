import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/verify_otp_screen.dart';
import '../../features/auth/screens/reset_password_screen.dart';
import '../../features/auth/services/auth_service.dart';
import '../../features/orders/screens/orders_list_screen.dart';
import '../../features/scanner/screens/scanner_screen.dart';
import '../../shared/screens/dashboard_screen.dart';
import '../../shared/screens/splash_screen.dart';
import '../../shared/screens/test_login_screen.dart';
import '../../shared/screens/quick_test_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/edit_profile_screen.dart';
import '../../features/tracking/screens/tracking_detail_screen.dart';
import '../../features/orders/screens/order_detail_screen.dart';

class AppRouter {
  static final AuthService _authService = AuthService();

  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    redirect: (context, state) {
      final isAuthenticated = _authService.isAuthenticated;
      final location = state.matchedLocation;
      final isOnAuthPage = location == '/login' ||
                          location == '/register' ||
                          location == '/forgot-password';
      final isOnSplash = location == '/splash';

      // If not authenticated and not on auth pages, redirect to login
      if (!isAuthenticated && !isOnAuthPage && !isOnSplash) {
        return '/login';
      }

      // If authenticated and on auth pages, redirect to dashboard
      if (isAuthenticated && isOnAuthPage) {
        return '/dashboard';
      }

      return null; // No redirect needed
    },
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Test Login Screen (for development)
      GoRoute(
        path: '/test-login',
        builder: (context, state) => const TestLoginScreen(),
      ),

      // Quick Test Screen (for development)
      GoRoute(
        path: '/quick-test',
        builder: (context, state) => const QuickTestScreen(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/verify-otp',
        builder: (context, state) {
          final email = state.extra as String;
          return VerifyOtpScreen(email: email);
        },
      ),
      GoRoute(
        path: '/reset-password',
        builder: (context, state) {
          final data = state.extra as Map<String, String>;
          return ResetPasswordScreen(
            email: data['email']!,
            otp: data['otp']!,
          );
        },
      ),

      // Main App Routes
      GoRoute(
        path: '/dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),

      // Orders Routes
      GoRoute(
        path: '/orders',
        builder: (context, state) => const OrdersListScreen(),
        routes: [
          GoRoute(
            path: 'create',
            builder: (context, state) => const CreateOrderScreen(),
          ),
          GoRoute(
            path: ':orderId',
            builder: (context, state) {
              final orderId = state.pathParameters['orderId']!;
              return OrderDetailScreen(orderId: orderId);
            },
            routes: [
              GoRoute(
                path: 'edit',
                builder: (context, state) {
                  final orderId = state.pathParameters['orderId']!;
                  return EditOrderScreen(orderId: orderId);
                },
              ),
              GoRoute(
                path: 'update-status',
                builder: (context, state) {
                  final orderId = state.pathParameters['orderId']!;
                  return UpdateOrderStatusScreen(orderId: orderId);
                },
              ),
            ],
          ),
        ],
      ),

      // Scanner Routes
      GoRoute(
        path: '/scanner',
        builder: (context, state) => const ScannerScreen(),
      ),
      GoRoute(
        path: '/profile/edit',
        builder: (context, state) => const EditProfileScreen(),
      ),

      // Tracking
      GoRoute(
        path: '/tracking',
        builder: (context, state) {
          final orderId = state.uri.queryParameters['orderId'];
          final trackingCode = state.uri.queryParameters['trackingCode'];
          return TrackingDetailScreen(
            orderId: orderId,
            trackingCode: trackingCode,
          );
        },
      ),

      // Tracking Routes
      GoRoute(
        path: '/tracking',
        builder: (context, state) => const TrackingScreen(),
        routes: [
          GoRoute(
            path: ':orderId',
            builder: (context, state) {
              final orderId = state.pathParameters['orderId']!;
              return OrderTrackingScreen(orderId: orderId);
            },
          ),
        ],
      ),

      // Profile Routes
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ProfileScreen(),
        routes: [
          GoRoute(
            path: 'edit',
            builder: (context, state) => const EditProfileScreen(),
          ),
          GoRoute(
            path: 'change-password',
            builder: (context, state) => const ChangePasswordScreen(),
          ),
        ],
      ),

      // Settings Routes
      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Lỗi'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Trang không tồn tại',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Đường dẫn "${state.matchedLocation}" không được tìm thấy.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Về trang chủ'),
            ),
          ],
        ),
      ),
    ),
  );
}

// Placeholder screens - these would be implemented in their respective feature folders

class CreateOrderScreen extends StatelessWidget {
  const CreateOrderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo đơn hàng'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: const Center(child: Text('Create Order Screen')),
    );
  }
}



class EditOrderScreen extends StatelessWidget {
  final String orderId;
  
  const EditOrderScreen({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chỉnh sửa đơn hàng'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: Center(child: Text('Edit Order Screen: $orderId')),
    );
  }
}

class UpdateOrderStatusScreen extends StatelessWidget {
  final String orderId;
  
  const UpdateOrderStatusScreen({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cập nhật trạng thái'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: Center(child: Text('Update Order Status Screen: $orderId')),
    );
  }
}

class TrackingScreen extends StatelessWidget {
  const TrackingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theo dõi đơn hàng'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: const Center(child: Text('Tracking Screen')),
    );
  }
}

class OrderTrackingScreen extends StatelessWidget {
  final String orderId;

  const OrderTrackingScreen({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theo dõi đơn hàng'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: Center(child: Text('Order Tracking Screen: $orderId')),
    );
  }
}





class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Đổi mật khẩu'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: const Center(child: Text('Change Password Screen')),
    );
  }
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cài đặt'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
      ),
      body: const Center(child: Text('Settings Screen')),
    );
  }
}
