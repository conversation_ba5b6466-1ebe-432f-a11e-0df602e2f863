class AppConstants {
  // App Info
  static const String appName = 'Flutter Logistics';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://api.logistics.com';
  static const String apiVersion = '/v1';
  static const Duration requestTimeout = Duration(seconds: 30);
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String refreshTokenKey = 'refresh_token';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Order Status
  static const String orderPending = 'pending';
  static const String orderConfirmed = 'confirmed';
  static const String orderInTransit = 'in_transit';
  static const String orderDelivered = 'delivered';
  static const String orderCancelled = 'cancelled';
  
  // User Roles
  static const String roleAdmin = 'admin';
  static const String roleDriver = 'driver';
  static const String roleCustomer = 'customer';
  static const String roleWarehouse = 'warehouse';
  
  // QR Code Types
  static const String qrTypeOrder = 'order';
  static const String qrTypePackage = 'package';
  static const String qrTypeLocation = 'location';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 500;
  
  // File Upload
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  
  // Map & Location
  static const double defaultLatitude = 21.0285; // Hanoi
  static const double defaultLongitude = 105.8542;
  static const double defaultZoom = 15.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Error Messages
  static const String networkError = 'Network connection error';
  static const String serverError = 'Server error occurred';
  static const String unknownError = 'Unknown error occurred';
  static const String validationError = 'Validation error';
  static const String authError = 'Authentication error';
  
  // Success Messages
  static const String loginSuccess = 'Login successful';
  static const String logoutSuccess = 'Logout successful';
  static const String orderCreated = 'Order created successfully';
  static const String orderUpdated = 'Order updated successfully';
  static const String profileUpdated = 'Profile updated successfully';
}
