import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/services/auth_service.dart';
import '../../core/constants/app_constants.dart';

class TestLoginScreen extends StatelessWidget {
  const TestLoginScreen({super.key});

  Future<void> _quickLogin(BuildContext context, String email, String password, String role) async {
    final authService = AuthService();
    
    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
    
    final result = await authService.login(email, password);
    
    // Hide loading
    if (context.mounted) {
      Navigator.of(context).pop();
    }
    
    if (result.isSuccess) {
      if (context.mounted) {
        context.go('/dashboard');
      }
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error ?? 'Login failed')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Login - Chọn vai trò'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 1,
        shadowColor: theme.colorScheme.shadow.withOpacity(0.1),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 32),
              
              // Header
              Icon(
                Icons.developer_mode,
                size: 80,
                color: theme.primaryColor,
              ),
              const SizedBox(height: 24),
              
              Text(
                'Demo Mode',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              
              Text(
                'Chọn vai trò để đăng nhập nhanh và test ứng dụng',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              
              // Quick login buttons
              _buildQuickLoginButton(
                context,
                'Admin',
                'Quản trị viên - Toàn quyền',
                Icons.admin_panel_settings,
                Colors.red,
                '<EMAIL>',
                '123456',
                AppConstants.roleAdmin,
              ),
              const SizedBox(height: 16),
              
              _buildQuickLoginButton(
                context,
                'Tài xế',
                'Nhận và giao đơn hàng',
                Icons.local_shipping,
                Colors.blue,
                '<EMAIL>',
                '123456',
                AppConstants.roleDriver,
              ),
              const SizedBox(height: 16),
              
              _buildQuickLoginButton(
                context,
                'Khách hàng',
                'Theo dõi đơn hàng',
                Icons.person,
                Colors.green,
                '<EMAIL>',
                '123456',
                AppConstants.roleCustomer,
              ),
              const SizedBox(height: 16),
              
              _buildQuickLoginButton(
                context,
                'Nhân viên kho',
                'Quản lý hàng hóa',
                Icons.warehouse,
                Colors.orange,
                '<EMAIL>',
                '123456',
                AppConstants.roleWarehouse,
              ),
              
              const Spacer(),
              
              // Manual login
              OutlinedButton(
                onPressed: () {
                  context.push('/login');
                },
                child: const Text('Đăng nhập thủ công'),
              ),
              
              const SizedBox(height: 16),
              
              // Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: theme.colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Thông tin test',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Tất cả tài khoản đều dùng mật khẩu: 123456\n'
                      '• Dữ liệu đơn hàng là mock data\n'
                      '• QR Scanner sẽ hoạt động với mã demo\n'
                      '• Tất cả tính năng đều có thể test được',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickLoginButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String email,
    String password,
    String role,
  ) {
    return Card(
      child: InkWell(
        onTap: () => _quickLogin(context, email, password, role),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
