import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/services/auth_service.dart';
import '../../features/orders/services/order_service.dart';
import '../../core/constants/app_constants.dart';

class QuickTestScreen extends StatefulWidget {
  const QuickTestScreen({super.key});

  @override
  State<QuickTestScreen> createState() => _QuickTestScreenState();
}

class _QuickTestScreenState extends State<QuickTestScreen> {
  final AuthService _authService = AuthService();
  final OrderService _orderService = OrderService();
  bool _isLoading = false;
  String _testResult = '';

  Future<void> _quickLogin() async {
    setState(() {
      _isLoading = true;
      _testResult = 'Đang đăng nhập...';
    });

    try {
      final result = await _authService.login('<EMAIL>', '123456');
      
      if (result.isSuccess) {
        setState(() {
          _testResult = 'Đăng nhập thành công!\nUser: ${result.user?.name}\nRole: ${result.user?.role}';
        });
        
        // Auto navigate to dashboard after 2 seconds
        Future.delayed(Duration(seconds: 2), () {
          if (mounted) {
            context.go('/dashboard');
          }
        });
      } else {
        setState(() {
          _testResult = 'Đăng nhập thất bại: ${result.error}';
        });
      }
    } catch (e) {
      setState(() {
        _testResult = 'Lỗi: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testOrders() async {
    setState(() {
      _isLoading = true;
      _testResult = 'Đang test orders...';
    });

    try {
      await _orderService.initialize();
      final orders = await _orderService.getOrders();
      final stats = await _orderService.getOrderStatistics();
      
      setState(() {
        _testResult = 'Test Orders thành công!\n'
            'Số đơn hàng: ${orders.length}\n'
            'Pending: ${stats[AppConstants.orderPending] ?? 0}\n'
            'Confirmed: ${stats[AppConstants.orderConfirmed] ?? 0}\n'
            'In Transit: ${stats[AppConstants.orderInTransit] ?? 0}\n'
            'Delivered: ${stats[AppConstants.orderDelivered] ?? 0}\n'
            'Cancelled: ${stats[AppConstants.orderCancelled] ?? 0}';
      });
    } catch (e) {
      setState(() {
        _testResult = 'Lỗi test orders: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quick Test'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 1,
        shadowColor: theme.colorScheme.shadow.withOpacity(0.1),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 32),
              
              // Header
              Icon(
                Icons.speed,
                size: 80,
                color: theme.primaryColor,
              ),
              const SizedBox(height: 24),
              
              Text(
                'Quick Test',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              
              Text(
                'Test nhanh các tính năng chính',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              
              // Test buttons
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _quickLogin,
                icon: const Icon(Icons.login),
                label: const Text('Test Login (Admin)'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
              const SizedBox(height: 16),
              
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _testOrders,
                icon: const Icon(Icons.shopping_bag),
                label: const Text('Test Orders Service'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
              const SizedBox(height: 16),
              
              OutlinedButton.icon(
                onPressed: () => context.push('/test-login'),
                icon: const Icon(Icons.person),
                label: const Text('Chọn User Login'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
              const SizedBox(height: 32),
              
              // Result display
              if (_testResult.isNotEmpty)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.colorScheme.outline.withOpacity(0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: theme.colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Kết quả test',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _testResult,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              
              if (_isLoading)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              
              const Spacer(),
              
              // Navigation buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => context.push('/login'),
                      child: const Text('Login Manual'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => context.go('/dashboard'),
                      child: const Text('Go Dashboard'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
