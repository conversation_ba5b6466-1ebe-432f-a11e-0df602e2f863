# Flutter Logistics - Hướng dẫn phát triển chi tiết

Ứng dụng quản lý logistics được xây dựng bằng Flutter với các tính năng quét mã QR, quản lý đơn hàng, theo dõi vận chuyển và hệ thống xác thực người dùng.

## 📋 Mục lục
1. [Tổng quan dự án](#tổng-quan-dự-án)
2. [Cài đặt và chạy](#cài-đặt-và-chạy)
3. [Kiến trúc dự án](#kiến-trúc-dự-án)
4. [Hướng dẫn phát triển](#hướng-dẫn-phát-triển)
5. [API Integration](#api-integration)
6. [Testing](#testing)
7. [Deployment](#deployment)

## 🎯 Tổng quan dự án

### Mục tiêu
Xây dựng ứng dụng quản lý logistics hoàn chỉnh với khả năng:
- Quản lý đơn hàng từ tạo đến giao hàng
- Quét mã QR/barcode để tracking
- H<PERSON> thống authentication đa vai trò
- Hoạt động offline và sync khi có internet
- Giao diện responsive cho mobile và web

### Đối tượng sử dụng
- **Admin**: Quản lý toàn bộ hệ thống
- **Tài xế**: Nhận và giao đơn hàng
- **Nhân viên kho**: Quản lý hàng hóa
- **Khách hàng**: Theo dõi đơn hàng

## 🚀 Tính năng chính

### 🔐 Xác thực & Quản lý người dùng
- Đăng nhập/Đăng ký với mã hóa mật khẩu
- Quản lý phiên đăng nhập với JWT token
- Hỗ trợ nhiều vai trò: Admin, Tài xế, Khách hàng, Nhân viên kho
- Quên mật khẩu và đổi mật khẩu

### 📦 Quản lý đơn hàng
- Tạo, chỉnh sửa và xóa đơn hàng
- Theo dõi trạng thái đơn hàng (Chờ xử lý, Đã xác nhận, Đang giao, Đã giao, Đã hủy)
- Tìm kiếm và lọc đơn hàng
- Cập nhật trạng thái đơn hàng real-time
- Quản lý thông tin khách hàng và địa chỉ giao hàng

### 📱 Quét mã QR/Barcode
- Quét mã QR và barcode với camera
- Hỗ trợ nhiều định dạng: QR Code, Code 128, EAN-13, UPC-A, v.v.
- Phân tích và xử lý các loại mã khác nhau (đơn hàng, gói hàng, vị trí)
- Giao diện quét trực quan với overlay và animation
- Điều khiển đèn flash và chuyển đổi camera

### 🚚 Theo dõi vận chuyển
- Theo dõi đơn hàng theo thời gian thực
- Timeline chi tiết của quá trình vận chuyển
- Cập nhật vị trí và trạng thái giao hàng
- Thông báo cho khách hàng

### 💾 Lưu trữ offline
- Sử dụng Hive để lưu trữ dữ liệu offline
- Đồng bộ dữ liệu khi có kết nối internet
- Cache thông tin đơn hàng và người dùng

## 🛠 Công nghệ sử dụng

### Framework & Language
- **Flutter 3.x** - Framework phát triển ứng dụng đa nền tảng
- **Dart** - Ngôn ngữ lập trình

### State Management
- **Provider** - Quản lý state đơn giản
- **Riverpod** - State management nâng cao

### Navigation
- **GoRouter** - Routing và navigation

### UI/UX
- **Material Design 3** - Thiết kế giao diện
- **Google Fonts** - Font chữ
- **Flutter SVG** - Hiển thị icon SVG
- **Shimmer** - Loading animation
- **Pull to Refresh** - Làm mới dữ liệu

### QR/Barcode Scanning
- **Mobile Scanner** - Quét mã QR/barcode
- **QR Flutter** - Tạo mã QR

### Networking & API
- **Dio** - HTTP client
- **JWT Decoder** - Giải mã JWT token

### Local Storage
- **Hive** - Database NoSQL local
- **Shared Preferences** - Lưu trữ cài đặt

### Utilities
- **Intl** - Định dạng ngày tháng và tiền tệ
- **UUID** - Tạo ID duy nhất
- **Permission Handler** - Quản lý quyền truy cập
- **Crypto** - Mã hóa dữ liệu

## 📱 Cài đặt và chạy ứng dụng

### Yêu cầu hệ thống
- Flutter SDK 3.0.0 trở lên
- Dart SDK 3.0.0 trở lên
- Android Studio hoặc VS Code
- Android SDK (cho Android)
- Xcode (cho iOS)

### Bước 1: Cài đặt Flutter
```bash
# Tải Flutter SDK từ https://flutter.dev/docs/get-started/install
# Thêm Flutter vào PATH
export PATH="$PATH:`pwd`/flutter/bin"

# Kiểm tra cài đặt
flutter doctor
```

### Bước 2: Clone và setup dự án
```bash
# Clone dự án
git clone <repository-url>
cd flutter-logistics

# Cài đặt dependencies
flutter pub get

# Tạo file generated (cho Hive adapters)
flutter packages pub run build_runner build
```

### Bước 3: Cấu hình
1. **Android**: Cập nhật `android/app/build.gradle` nếu cần
2. **iOS**: Cập nhật `ios/Runner/Info.plist` nếu cần
3. **API**: Cập nhật `baseUrl` trong `lib/core/constants/app_constants.dart`

### Bước 4: Chạy ứng dụng
```bash
# Chạy trên emulator/device
flutter run

# Build APK cho Android
flutter build apk --release

# Build IPA cho iOS
flutter build ios --release
```

## 🏗 Kiến trúc dự án

### Clean Architecture
Dự án sử dụng Clean Architecture với các layer:

```
lib/
├── core/                    # Core functionality
│   ├── constants/          # App constants & configs
│   ├── models/            # Core data models
│   ├── services/          # Core services (router, etc)
│   └── utils/             # Utility functions
├── features/              # Feature modules (Clean Architecture)
│   ├── auth/             # Authentication feature
│   │   ├── models/       # Auth-specific models
│   │   ├── services/     # Auth business logic
│   │   ├── screens/      # Auth UI screens
│   │   └── widgets/      # Auth-specific widgets
│   ├── orders/           # Order management feature
│   ├── scanner/          # QR/Barcode scanning feature
│   └── tracking/         # Order tracking feature
├── shared/               # Shared components
│   ├── themes/          # App themes & styling
│   ├── widgets/         # Reusable UI components
│   └── screens/         # Common screens (splash, dashboard)
└── main.dart            # App entry point
```

### State Management
- **Provider**: Cho state management đơn giản
- **Riverpod**: Cho state management phức tạp hơn
- **Hive**: Local database cho offline storage

### Navigation
- **GoRouter**: Declarative routing với type-safe navigation

## 🏗 Cấu trúc dự án chi tiết

```
lib/
├── core/                    # Core functionality
│   ├── constants/          # App constants
│   ├── models/            # Core models
│   ├── services/          # Core services
│   └── utils/             # Utilities
├── features/              # Feature modules
│   ├── auth/             # Authentication
│   │   ├── models/
│   │   ├── services/
│   │   ├── screens/
│   │   └── widgets/
│   ├── orders/           # Order management
│   ├── scanner/          # QR/Barcode scanning
│   └── tracking/         # Order tracking
├── shared/               # Shared components
│   ├── themes/          # App themes
│   ├── widgets/         # Reusable widgets
│   └── screens/         # Shared screens
└── main.dart            # App entry point
```

## 🔧 Cấu hình API

Ứng dụng sử dụng REST API để giao tiếp với backend. Cập nhật cấu hình API trong:

```dart
// lib/core/constants/app_constants.dart
class AppConstants {
  static const String baseUrl = 'https://your-api-domain.com';
  static const String apiVersion = '/v1';
}
```

### API Endpoints
- `POST /auth/login` - Đăng nhập
- `POST /auth/register` - Đăng ký
- `GET /orders` - Lấy danh sách đơn hàng
- `POST /orders` - Tạo đơn hàng mới
- `PUT /orders/:id/status` - Cập nhật trạng thái đơn hàng

## 🧪 Testing

```bash
# Chạy unit tests
flutter test

# Chạy integration tests
flutter drive --target=test_driver/app.dart
```

## 📝 Hướng dẫn sử dụng

### 1. Đăng nhập/Đăng ký
- Mở ứng dụng và chọn "Đăng nhập" hoặc "Đăng ký"
- Nhập thông tin và chọn vai trò phù hợp
- Sau khi đăng nhập thành công, bạn sẽ được chuyển đến Dashboard

### 2. Quét mã QR
- Từ Dashboard, chọn "Quét mã QR" hoặc icon scanner
- Cấp quyền truy cập camera khi được yêu cầu
- Đưa camera về phía mã QR để quét
- Xử lý kết quả quét theo loại mã

### 3. Quản lý đơn hàng
- Chọn "Đơn hàng" từ bottom navigation
- Xem danh sách đơn hàng với các tab trạng thái
- Tìm kiếm đơn hàng bằng số đơn hàng hoặc tên khách hàng
- Cập nhật trạng thái đơn hàng bằng menu actions

### 4. Tạo đơn hàng mới
- Từ màn hình đơn hàng, chọn nút "+"
- Điền thông tin đơn hàng và khách hàng
- Thêm sản phẩm và địa chỉ giao hàng
- Lưu đơn hàng

## 🤝 Đóng góp

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 📞 Liên hệ

- Email: <EMAIL>
- Website: https://flutterlogistics.com
- GitHub: https://github.com/your-username/flutter-logistics

## 🙏 Acknowledgments

- Flutter team cho framework tuyệt vời
- Cộng đồng Flutter Việt Nam
- Các thư viện mã nguồn mở được sử dụng trong dự án
