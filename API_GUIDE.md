# 🌐 API Integration Guide

## 📋 Tổng quan
Tài liệu này mô tả cách tích hợp ứng dụng Flutter Logistics với backend API.

## 🔧 Cấu hình API

### Base Configuration
```dart
// lib/core/constants/app_constants.dart
class AppConstants {
  // API Configuration
  static const String baseUrl = 'https://api.logistics.com';
  static const String apiVersion = '/v1';
  static const Duration requestTimeout = Duration(seconds: 30);
}
```

### Dio Setup
```dart
// lib/core/services/api_service.dart
class ApiService {
  static final Dio _dio = Dio();
  
  static void initialize() {
    _dio.options.baseUrl = AppConstants.baseUrl + AppConstants.apiVersion;
    _dio.options.connectTimeout = AppConstants.requestTimeout;
    _dio.options.receiveTimeout = AppConstants.requestTimeout;
    
    // Add interceptors
    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(LoggingInterceptor());
  }
}
```

## 🔐 Authentication Endpoints

### POST /auth/login
Đăng nhập người dùng

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "hashed_password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "refresh_token": "refresh_token_here",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "customer",
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

**Flutter Implementation:**
```dart
Future<AuthResult> login(String email, String password) async {
  try {
    final hashedPassword = _hashPassword(password);
    final response = await _dio.post('/auth/login', data: {
      'email': email,
      'password': hashedPassword,
    });

    if (response.statusCode == 200) {
      final data = response.data['data'];
      _token = data['token'];
      _currentUser = UserModel.fromJson(data['user']);
      await _saveAuth();
      return AuthResult.success(_currentUser!);
    }
  } on DioException catch (e) {
    return AuthResult.failure(_handleDioError(e));
  }
  return AuthResult.failure('Login failed');
}
```

### POST /auth/register
Đăng ký người dùng mới

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "hashed_password",
  "name": "New User",
  "phone": "0123456789",
  "role": "customer"
}
```

### POST /auth/refresh
Làm mới token

**Request:**
```json
{
  "refresh_token": "refresh_token_here"
}
```

## 📦 Orders Endpoints

### GET /orders
Lấy danh sách đơn hàng

**Query Parameters:**
- `page`: Số trang (default: 1)
- `limit`: Số lượng per page (default: 20)
- `status`: Lọc theo trạng thái
- `search`: Tìm kiếm theo từ khóa

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_id",
        "order_number": "ORD-2024-001",
        "customer_id": "customer_id",
        "customer_name": "Customer Name",
        "customer_phone": "0123456789",
        "status": "pending",
        "total_amount": 350000,
        "currency": "VND",
        "pickup_address": {
          "street": "123 Street",
          "city": "Hanoi",
          "state": "Hanoi",
          "zip_code": "100000",
          "country": "Vietnam"
        },
        "delivery_address": {
          "street": "456 Avenue",
          "city": "Ho Chi Minh",
          "state": "Ho Chi Minh",
          "zip_code": "700000",
          "country": "Vietnam"
        },
        "items": [
          {
            "id": "item_id",
            "name": "Product Name",
            "description": "Product Description",
            "quantity": 2,
            "unit_price": 100000,
            "total_price": 200000
          }
        ],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "total_items": 200,
      "per_page": 20
    }
  }
}
```

**Flutter Implementation:**
```dart
Future<List<OrderModel>> getOrders({
  int page = 1,
  int limit = 20,
  String? status,
  String? search,
}) async {
  try {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };
    
    if (status != null) queryParams['status'] = status;
    if (search != null) queryParams['search'] = search;

    final response = await _dio.get('/orders', queryParameters: queryParams);
    
    if (response.statusCode == 200) {
      final ordersJson = response.data['data']['orders'] as List;
      return ordersJson.map((json) => OrderModel.fromJson(json)).toList();
    }
  } catch (e) {
    // Return cached data on error
    return _getCachedOrders(status: status, search: search);
  }
  return [];
}
```

### GET /orders/{id}
Lấy chi tiết đơn hàng

**Response:**
```json
{
  "success": true,
  "data": {
    "order": {
      // Order object như trên
    }
  }
}
```

### POST /orders
Tạo đơn hàng mới

**Request:**
```json
{
  "customer_name": "Customer Name",
  "customer_phone": "0123456789",
  "pickup_address": {
    "street": "123 Street",
    "city": "Hanoi",
    "state": "Hanoi",
    "zip_code": "100000",
    "country": "Vietnam"
  },
  "delivery_address": {
    "street": "456 Avenue",
    "city": "Ho Chi Minh",
    "state": "Ho Chi Minh", 
    "zip_code": "700000",
    "country": "Vietnam"
  },
  "items": [
    {
      "name": "Product Name",
      "description": "Product Description",
      "quantity": 2,
      "unit_price": 100000
    }
  ]
}
```

### PUT /orders/{id}/status
Cập nhật trạng thái đơn hàng

**Request:**
```json
{
  "status": "confirmed",
  "notes": "Order confirmed by admin"
}
```

## 📊 Statistics Endpoints

### GET /orders/statistics
Lấy thống kê đơn hàng

**Response:**
```json
{
  "success": true,
  "data": {
    "statistics": {
      "pending": 15,
      "confirmed": 25,
      "in_transit": 10,
      "delivered": 100,
      "cancelled": 5
    }
  }
}
```

## 🔍 Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "email": ["Email is required"],
      "password": ["Password must be at least 8 characters"]
    }
  }
}
```

### Flutter Error Handling
```dart
String _handleDioError(DioException e) {
  switch (e.type) {
    case DioExceptionType.connectionTimeout:
    case DioExceptionType.receiveTimeout:
      return 'Connection timeout. Please check your internet.';
    case DioExceptionType.badResponse:
      final statusCode = e.response?.statusCode;
      final message = e.response?.data?['error']?['message'] ?? 'Server error';
      return '$message (Status: $statusCode)';
    case DioExceptionType.unknown:
      return 'Network error. Please check your connection.';
    default:
      return 'An unexpected error occurred';
  }
}
```

## 🔒 Authentication Headers

### JWT Token
Tất cả API calls (trừ login/register) cần header:
```
Authorization: Bearer <jwt_token>
```

### Interceptor Implementation
```dart
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = AuthService().token;
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Token expired, logout user
      AuthService().logout();
    }
    handler.next(err);
  }
}
```

## 📱 Offline Support

### Caching Strategy
```dart
Future<List<OrderModel>> getOrders() async {
  try {
    // Try to fetch from API
    final orders = await _fetchFromApi();
    
    // Cache the results
    await _cacheOrders(orders);
    
    return orders;
  } catch (e) {
    // Return cached data if API fails
    return await _getCachedOrders();
  }
}
```

### Sync Strategy
```dart
Future<void> syncOfflineChanges() async {
  final pendingChanges = await _getPendingChanges();
  
  for (final change in pendingChanges) {
    try {
      await _syncChange(change);
      await _markAsSynced(change);
    } catch (e) {
      // Keep for next sync attempt
      print('Failed to sync change: $e');
    }
  }
}
```

## 🧪 Testing API Integration

### Mock API for Development
```dart
class MockApiService implements ApiService {
  @override
  Future<List<OrderModel>> getOrders() async {
    // Return mock data
    await Future.delayed(Duration(seconds: 1)); // Simulate network delay
    return [
      OrderModel(
        id: 'mock_1',
        orderNumber: 'ORD-MOCK-001',
        // ... other mock data
      ),
    ];
  }
}
```

### Environment Configuration
```dart
class Environment {
  static const String apiUrl = String.fromEnvironment(
    'API_URL',
    defaultValue: 'https://api.logistics.com',
  );
  
  static const bool useMockApi = bool.fromEnvironment(
    'USE_MOCK_API',
    defaultValue: false,
  );
}
```

## 🚀 Production Considerations

### Security
- Sử dụng HTTPS cho tất cả API calls
- Implement certificate pinning
- Validate tất cả input data
- Không log sensitive data

### Performance
- Implement request caching
- Use pagination cho large datasets
- Compress request/response data
- Monitor API response times

### Monitoring
- Log all API errors
- Track API usage metrics
- Monitor network connectivity
- Implement retry mechanisms

---

## 📞 Backend Requirements

Để ứng dụng hoạt động đầy đủ, backend cần implement:

1. **Authentication System**
   - JWT token generation/validation
   - Password hashing (bcrypt recommended)
   - Refresh token mechanism

2. **Order Management**
   - CRUD operations for orders
   - Status tracking
   - Search and filtering

3. **File Upload**
   - Image upload for orders
   - File validation and processing

4. **Real-time Updates** (Optional)
   - WebSocket for live updates
   - Push notifications

5. **Analytics** (Optional)
   - Order statistics
   - Performance metrics

Happy coding! 🎉
