name: flutter_logistics
description: A Flutter logistics application with QR scanning, order management, and tracking features.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.9
  google_fonts: ^6.1.0
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # QR Code & Barcode Scanner
  mobile_scanner: ^3.5.6
  qr_flutter: ^4.1.0
  
  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Authentication & Security
  crypto: ^3.0.3
  jwt_decoder: ^2.0.1
  
  # Date & Time
  intl: ^0.19.0
  
  # Utilities
  uuid: ^4.2.1
  permission_handler: ^11.1.0
  
  # UI Components
  flutter_spinkit: ^5.2.0
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0
  
  # Image & File Handling
  image_picker: ^1.0.4
  cached_network_image: ^3.3.0
  signature: ^5.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/
