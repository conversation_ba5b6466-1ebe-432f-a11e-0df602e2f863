# 🔐 Password Reset Flow Guide - Hướng dẫn test flow khôi phục mật khẩu

## 🎯 Flow đã được sửa và hoàn thiện

### ✅ **Complete Password Reset Flow:**

```
1. Login Screen
   ↓ [Quên mật khẩu?]
2. Forgot Password Screen
   ↓ [Nhập email + Gửi OTP]
3. Verify OTP Screen  
   ↓ [Nhập OTP + X<PERSON><PERSON> thực]
4. Reset Password Screen
   ↓ [Nhập mật khẩu mới + Đổi mật khẩu]
5. Success Dialog
   ↓ [Đăng nhập ngay]
6. Login Screen
```

---

## 🧪 Hướng dẫn test từng bước

### **Bước 1: Forgot Password Screen**
1. Từ Login screen, click **"Quên mật khẩu?"**
2. Nhập email: `<EMAIL>`
3. Click **"Gửi mã OTP"**
4. ✅ **Expected**: <PERSON><PERSON><PERSON> thị SnackBar "OTP đã được gửi"
5. ✅ **Expected**: Navigate đến Verify OTP Screen

### **Bước 2: Verify OTP Screen**
1. Nhập OTP: **`123456`** (mock OTP)
2. Click **"Xác thực OTP"** hoặc auto-submit
3. ✅ **Expected**: Hiển thị SnackBar "OTP xác thực thành công"
4. ✅ **Expected**: Navigate đến Reset Password Screen

### **Bước 3: Reset Password Screen**
1. Nhập mật khẩu mới: `NewPassword123`
2. Xác nhận mật khẩu: `NewPassword123`
3. Click **"Đổi mật khẩu"**
4. ✅ **Expected**: Hiển thị SnackBar "Đổi mật khẩu thành công"
5. ✅ **Expected**: Hiển thị Success Dialog
6. Click **"Đăng nhập ngay"**
7. ✅ **Expected**: Navigate về Login Screen

---

## 🔧 Technical Implementation

### **1. Routes Configuration:**
```dart
// app_router.dart
GoRoute(
  path: '/forgot-password',
  builder: (context, state) => const ForgotPasswordScreen(),
),
GoRoute(
  path: '/verify-otp',
  builder: (context, state) {
    final email = state.extra as String;
    return VerifyOtpScreen(email: email);
  },
),
GoRoute(
  path: '/reset-password',
  builder: (context, state) {
    final data = state.extra as Map<String, String>;
    return ResetPasswordScreen(
      email: data['email']!,
      otp: data['otp']!,
    );
  },
),
```

### **2. Navigation Flow:**
```dart
// Forgot Password → Verify OTP
context.push('/verify-otp', extra: email);

// Verify OTP → Reset Password
context.push('/reset-password', extra: {
  'email': widget.email,
  'otp': _otpCode,
});

// Reset Password → Login
context.go('/login');
```

### **3. Mock Data for Testing:**
```dart
// AuthService mock implementations
forgotPassword(email) → Success: "OTP đã được gửi"
verifyOtp(email, "123456") → Success: "OTP xác thực thành công"
resetPassword(email, "123456", newPassword) → Success: "Mật khẩu đã được đổi thành công"
```

---

## 🎨 UI Consistency Updates

### **Headers đã được cập nhật:**
```dart
// Tất cả auth screens đều có primary header
appBar: AppBar(
  title: const Text('Screen Title'),
  backgroundColor: theme.colorScheme.primary,
  foregroundColor: theme.colorScheme.onPrimary,
  elevation: 0,
),
```

### **Screens đã cập nhật:**
- ✅ **Register Screen**: Primary header
- ✅ **Forgot Password Screen**: Primary header  
- ✅ **Verify OTP Screen**: Primary header
- ✅ **Reset Password Screen**: Primary header

---

## 🐛 Troubleshooting

### **Nếu flow không hoạt động:**

#### **1. Check Console Logs:**
```
🔥 DEBUG: Navigating to verify-otp with email: <EMAIL>
```

#### **2. Common Issues:**
- **Email validation**: Phải đúng format email
- **OTP không đúng**: Phải nhập `123456`
- **Password validation**: Phải có ít nhất 1 chữ hoa, 1 chữ thường, 1 số
- **Navigation error**: Check routes trong app_router.dart

#### **3. Validation Rules:**
```dart
// Email validation
RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')

// Password validation  
- Minimum 6 characters
- At least 1 uppercase letter
- At least 1 lowercase letter  
- At least 1 number
```

---

## 🧪 Test Cases

### **Test Case 1: Happy Path**
```
Input: <EMAIL> → 123456 → NewPassword123
Expected: Success flow từ đầu đến cuối
```

### **Test Case 2: Invalid Email**
```
Input: invalid-email
Expected: Error "Email không hợp lệ"
```

### **Test Case 3: Wrong OTP**
```
Input: <EMAIL> → 111111
Expected: Error "Mã OTP không đúng"
```

### **Test Case 4: Weak Password**
```
Input: <EMAIL> → 123456 → 123
Expected: Error "Mật khẩu phải có ít nhất 6 ký tự"
```

### **Test Case 5: Password Mismatch**
```
Input: NewPassword123 ≠ DifferentPassword123
Expected: Error "Mật khẩu xác nhận không khớp"
```

---

## 🔍 Debug Information

### **Mock OTP Code:**
```
📱 OTP: 123456
```

### **Test Email:**
```
📧 Email: <EMAIL>
```

### **Test Password:**
```
🔐 Password: NewPassword123
```

### **Navigation Debug:**
```dart
// Added debug logs in forgot_password_screen.dart
print('🔥 DEBUG: Navigating to verify-otp with email: $email');
```

---

## 🎯 Expected User Experience

### **Smooth Flow:**
1. **User forgets password** → Click "Quên mật khẩu?"
2. **Enter email** → Click "Gửi mã OTP"
3. **See success message** → Auto navigate to OTP screen
4. **Enter OTP** → Auto verify when complete
5. **See success message** → Auto navigate to reset screen
6. **Enter new password** → Click "Đổi mật khẩu"
7. **See success dialog** → Click "Đăng nhập ngay"
8. **Back to login** → Can login with new password

### **Visual Feedback:**
- ✅ **Loading states**: Buttons show loading spinner
- ✅ **Success messages**: Green SnackBars
- ✅ **Error messages**: Red SnackBars  
- ✅ **Form validation**: Real-time validation
- ✅ **Auto-focus**: Smooth input transitions

---

## 🚀 Production Considerations

### **Real API Integration:**
```dart
// Replace mock implementations with real API calls
Future<ForgotPasswordResult> forgotPassword(String email) async {
  final response = await _dio.post('/auth/forgot-password', data: {
    'email': email,
  });
  // Handle response...
}
```

### **Security Enhancements:**
- **Rate limiting**: Prevent spam OTP requests
- **OTP expiration**: Time-limited OTP codes
- **Secure storage**: Encrypted password storage
- **Audit logging**: Track password reset attempts

### **UX Improvements:**
- **Email templates**: Professional OTP emails
- **SMS option**: Alternative to email OTP
- **Progress indicator**: Show current step
- **Auto-fill OTP**: From SMS/Email

---

## ✅ Summary

**✅ Problem Solved:** Password reset flow now works correctly

**✅ Flow Fixed:** Forgot Password → Verify OTP → Reset Password → Login

**✅ UI Updated:** All auth screens have consistent primary headers

**✅ Testing:** Use email: `<EMAIL>`, OTP: `123456`

**✅ Debug:** Added console logs for navigation tracking

**Password reset flow is now complete and working perfectly!** 🎉
