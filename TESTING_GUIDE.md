# 🧪 Flutter Logistics - Hướng dẫn Test

## 🎯 Mục tiêu
Hướng dẫn chi tiết cách test ứng dụng Flutter Logistics với mock data (không cần API backend).

---

## 🚀 Bắt đầu test

### 1. Chạy ứng dụng
```bash
flutter run -d chrome
```

### 2. <PERSON><PERSON><PERSON> hình đầu tiên: Quick Test
Sau khi splash screen, bạn sẽ thấy màn hình **Quick Test** với các options:

#### 🔐 **Test Login (Admin)**
- Click để test đăng nhập với tài khoản admin
- Tự động login với: `<EMAIL>` / `123456`
- Sau 2 giây sẽ tự động chuyển đến Dashboard

#### 📦 **Test Orders Service**
- Click để test service lấy danh sách đơn hàng
- Hiển thị thống kê số lượng đơn hàng theo trạng thái
- <PERSON><PERSON><PERSON> tra mock data có hoạt động không

#### 👥 **Chọn User Login**
- <PERSON><PERSON>ển đến màn hình chọn vai trò để login
- Test với các user khác nhau

---

## 👥 Các tài khoản test

### 🔴 **Admin** (Toàn quyền)
- **Email**: `<EMAIL>`
- **Password**: `123456`
- **Quyền**: Xem tất cả, quản lý toàn bộ hệ thống

### 🔵 **Tài xế** (Driver)
- **Email**: `<EMAIL>`
- **Password**: `123456`
- **Quyền**: Nhận đơn hàng, cập nhật trạng thái giao hàng

### 🟢 **Khách hàng** (Customer)
- **Email**: `<EMAIL>`
- **Password**: `123456`
- **Quyền**: Xem đơn hàng của mình, theo dõi trạng thái

### 🟠 **Nhân viên kho** (Warehouse)
- **Email**: `<EMAIL>`
- **Password**: `123456`
- **Quyền**: Quản lý hàng hóa, cập nhật trạng thái đơn hàng

---

## 📱 Test các tính năng chính

### 🏠 **Dashboard**
Sau khi login, bạn sẽ thấy:
- **Welcome card** với thông tin user
- **Quick actions**: 4 nút chức năng chính
- **Statistics**: Thống kê đơn hàng real-time
- **Bottom navigation**: 5 tabs chính

### 📦 **Orders Management**
Click vào tab "Đơn hàng" hoặc "Danh sách đơn hàng":

#### ✅ **Danh sách đơn hàng**
- 5 đơn hàng mẫu với trạng thái khác nhau
- Filter theo trạng thái (tabs ở trên)
- Search theo mã đơn hàng hoặc tên khách hàng
- Pull to refresh để reload data

#### ✅ **Chi tiết đơn hàng**
- Click vào một đơn hàng để xem chi tiết
- Thông tin khách hàng, địa chỉ, sản phẩm
- Timeline trạng thái đơn hàng

#### ✅ **Cập nhật trạng thái**
- Click vào menu 3 chấm trên order card
- Chọn trạng thái mới
- Thay đổi sẽ được lưu và hiển thị ngay

### 📱 **QR Scanner**
Click vào tab "Quét mã" hoặc icon scanner:

#### ✅ **Camera Scanner**
- Cấp quyền camera khi được yêu cầu
- Overlay với animation scanning line
- Điều khiển flash và switch camera

#### ✅ **Test QR Codes**
Tạo QR code với nội dung sau để test:
- `ORDER:order_001` - Mở đơn hàng 001
- `ORDER:order_002` - Mở đơn hàng 002
- `PACKAGE:PKG001` - Thông tin gói hàng
- `https://google.com` - Mở URL

### 🔐 **Authentication Flow**

#### ✅ **Login**
- Form validation
- Loading states
- Error handling
- Auto redirect sau login

#### ✅ **Forgot Password**
- Nhập email → Gửi OTP
- Nhập OTP: `123456` (demo)
- Đổi mật khẩu mới
- Success dialog

#### ✅ **Register**
- Form đầy đủ với validation
- Chọn vai trò
- Password strength check

---

## 📊 Mock Data có sẵn

### 📦 **5 Đơn hàng mẫu**

1. **ORD-2024-001** - Pending
   - Khách: Nguyễn Văn An
   - Sản phẩm: Laptop Dell XPS 13
   - Giá: 250,000 VNĐ

2. **ORD-2024-002** - Confirmed
   - Khách: Trần Thị Bình
   - Sản phẩm: iPhone 15
   - Tài xế: Lê Văn Tài

3. **ORD-2024-003** - In Transit
   - Khách: Phạm Minh Cường
   - Sản phẩm: iPad Pro
   - Tài xế: Hoàng Văn Sơn

4. **ORD-2024-004** - Delivered
   - Khách: Vũ Thị Dung
   - Sản phẩm: AirPods Pro
   - Đã giao thành công

5. **ORD-2024-005** - Cancelled
   - Khách: Đỗ Thanh Long
   - Sản phẩm: Apple Watch
   - Lý do: Khách hàng hủy

### 📊 **Statistics**
- Pending: 1 đơn
- Confirmed: 1 đơn
- In Transit: 1 đơn
- Delivered: 1 đơn
- Cancelled: 1 đơn

---

## 🧪 Test Scenarios

### Scenario 1: Admin quản lý đơn hàng
1. Login với <EMAIL>
2. Xem dashboard statistics
3. Vào Orders → Xem tất cả đơn hàng
4. Filter theo trạng thái "Pending"
5. Cập nhật đơn hàng từ Pending → Confirmed
6. Kiểm tra statistics đã thay đổi

### Scenario 2: Tài xế giao hàng
1. Login với <EMAIL>
2. Xem đơn hàng được assign
3. Quét QR code: `ORDER:order_002`
4. Cập nhật trạng thái từ Confirmed → In Transit
5. Cập nhật từ In Transit → Delivered

### Scenario 3: Khách hàng theo dõi
1. Login với <EMAIL>
2. Xem đơn hàng của mình
3. Theo dõi trạng thái real-time
4. Sử dụng QR scanner để track

### Scenario 4: Forgot Password Flow
1. Từ login screen → Click "Quên mật khẩu"
2. Nhập email bất kỳ
3. Nhập OTP: `123456`
4. Tạo mật khẩu mới
5. Login với mật khẩu mới

---

## 🔍 Kiểm tra tính năng

### ✅ **UI/UX**
- [ ] Responsive design trên các màn hình
- [ ] Loading states hiển thị đúng
- [ ] Error messages rõ ràng
- [ ] Navigation smooth
- [ ] Animations mượt mà

### ✅ **Functionality**
- [ ] Login/logout hoạt động
- [ ] CRUD orders hoạt động
- [ ] Search/filter hoạt động
- [ ] QR scanner hoạt động
- [ ] Statistics cập nhật real-time

### ✅ **Data Management**
- [ ] Offline storage với Hive
- [ ] Data persistence sau restart
- [ ] Cache management
- [ ] State management

### ✅ **Performance**
- [ ] App khởi động nhanh
- [ ] Smooth scrolling
- [ ] Memory usage hợp lý
- [ ] Battery usage tối ưu

---

## 🐛 Troubleshooting

### Lỗi thường gặp:

#### 1. **Camera không hoạt động**
- Cấp quyền camera trong browser
- Chrome → Settings → Privacy → Camera

#### 2. **Data không hiển thị**
- Check console logs
- Restart app với `R`
- Clear browser cache

#### 3. **Navigation lỗi**
- Check GoRouter configuration
- Verify route paths

#### 4. **Build errors**
```bash
flutter clean
flutter pub get
flutter run
```

---

## 📝 Test Checklist

### 🔐 Authentication
- [ ] Login với tất cả user types
- [ ] Logout hoạt động
- [ ] Forgot password flow
- [ ] Register form validation
- [ ] Session persistence

### 📦 Orders
- [ ] Load danh sách đơn hàng
- [ ] Filter theo trạng thái
- [ ] Search theo keyword
- [ ] Update trạng thái
- [ ] View chi tiết đơn hàng

### 📱 QR Scanner
- [ ] Mở camera
- [ ] Quét QR code
- [ ] Parse kết quả đúng
- [ ] Handle các loại QR khác nhau
- [ ] Error handling

### 🏠 Dashboard
- [ ] Statistics hiển thị đúng
- [ ] Quick actions hoạt động
- [ ] User info hiển thị
- [ ] Navigation tabs

### 💾 Offline
- [ ] Data cache locally
- [ ] Hoạt động khi offline
- [ ] Sync khi online lại

---

## 🚀 Next Steps

Sau khi test xong mock version:

1. **Implement real API**
   - Uncomment real API code
   - Comment mock implementations
   - Update baseUrl

2. **Add more features**
   - Push notifications
   - Real-time updates
   - Advanced analytics

3. **Deploy**
   - Build for production
   - Deploy to app stores
   - Setup CI/CD

Happy testing! 🎉
