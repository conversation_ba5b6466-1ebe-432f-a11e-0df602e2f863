#!/bin/bash

# Flutter Logistics - Generate Android Keystore Script

echo "🔐 Generating Android Keystore for Flutter Logistics..."

# Keystore configuration
KEYSTORE_NAME="flutter-logistics-key.jks"
KEY_ALIAS="flutter-logistics"
VALIDITY_DAYS=10000

# Check if keystore already exists
if [ -f "$KEYSTORE_NAME" ]; then
    echo "⚠️  Keystore already exists: $KEYSTORE_NAME"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Keystore generation cancelled."
        exit 1
    fi
    rm "$KEYSTORE_NAME"
fi

# Generate keystore
echo "📝 Please provide the following information for your keystore:"

keytool -genkey -v \
    -keystore "$KEYSTORE_NAME" \
    -keyalg RSA \
    -keysize 2048 \
    -validity $VALIDITY_DAYS \
    -alias "$KEY_ALIAS" \
    -dname "CN=Flutter Logistics, OU=Development, O=Logistics Company, L=Ho Chi Minh City, ST=Ho Chi Minh, C=VN" \
    -storepass flutter123456 \
    -keypass flutter123456

if [ $? -eq 0 ]; then
    echo "✅ Keystore generated successfully: $KEYSTORE_NAME"
    echo "📋 Keystore details:"
    echo "   - File: $KEYSTORE_NAME"
    echo "   - Alias: $KEY_ALIAS"
    echo "   - Password: flutter123456"
    echo "   - Validity: $VALIDITY_DAYS days"
    echo ""
    echo "🔒 IMPORTANT: Keep this keystore file safe!"
    echo "   - Back it up to a secure location"
    echo "   - Never commit it to version control"
    echo "   - You'll need it for all future app updates"
    echo ""
    echo "📱 To build release APK, run:"
    echo "   flutter build apk --release"
    echo ""
    echo "📦 To build App Bundle, run:"
    echo "   flutter build appbundle --release"
else
    echo "❌ Failed to generate keystore"
    exit 1
fi
