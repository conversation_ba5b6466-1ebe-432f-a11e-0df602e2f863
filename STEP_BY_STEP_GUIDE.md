# 👣 Flutter Logistics - Hướng dẫn từng bước

## 🎯 Mục tiêu
Hướng dẫn chi tiết từng bước để bạn có thể code theo và hiểu rõ cách xây dựng ứng dụng.

---

## 📚 Bước 1: Setup Environment

### 1.1 Cài đặt Flutter
```bash
# Download Flutter SDK từ https://flutter.dev
# Giải nén và thêm vào PATH
export PATH="$PATH:/path/to/flutter/bin"

# Kiểm tra cài đặt
flutter doctor
```

### 1.2 Setup IDE
- **VS Code**: Cài extension Flutter và Dart
- **Android Studio**: Cài plugin Flutter

### 1.3 Tạo dự án mới
```bash
flutter create flutter_logistics --org com.logistics.app
cd flutter_logistics
```

---

## 🏗 Bước 2: Cấu trúc dự án

### 2.1 <PERSON><PERSON><PERSON> <PERSON><PERSON> m<PERSON>
```bash
mkdir -p lib/{core,features,shared}
mkdir -p lib/core/{constants,models,services,utils}
mkdir -p lib/features/{auth,orders,scanner,tracking}
mkdir -p lib/shared/{themes,widgets,screens}
```

### 2.2 Cập nhật pubspec.yaml
```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # Navigation
  go_router: ^12.1.3
  
  # HTTP & API
  dio: ^5.4.0
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # QR Scanner
  mobile_scanner: ^3.5.6
  
  # UI
  google_fonts: ^6.1.0
```

### 2.3 Chạy pub get
```bash
flutter pub get
```

---

## 🎨 Bước 3: Tạo Theme System

### 3.1 Tạo AppTheme
```dart
// lib/shared/themes/app_theme.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color backgroundColor = Color(0xFFF5F5F5);
  
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      textTheme: GoogleFonts.robotoTextTheme(),
      // ... other theme properties
    );
  }
}
```

### 3.2 Test theme
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'shared/themes/app_theme.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Logistics',
      theme: AppTheme.lightTheme,
      home: Scaffold(
        appBar: AppBar(title: Text('Flutter Logistics')),
        body: Center(child: Text('Hello World!')),
      ),
    );
  }
}
```

### 3.3 Chạy test
```bash
flutter run
```

---

## 🔐 Bước 4: Authentication System

### 4.1 Tạo User Model
```dart
// lib/core/models/user_model.dart
class UserModel {
  final String id;
  final String email;
  final String name;
  final String role;
  
  UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
  });
  
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      role: json['role'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role,
    };
  }
}
```

### 4.2 Tạo Auth Models
```dart
// lib/features/auth/models/auth_models.dart
class AuthResult {
  final bool isSuccess;
  final UserModel? user;
  final String? error;
  
  AuthResult._({required this.isSuccess, this.user, this.error});
  
  factory AuthResult.success(UserModel user) {
    return AuthResult._(isSuccess: true, user: user);
  }
  
  factory AuthResult.failure(String error) {
    return AuthResult._(isSuccess: false, error: error);
  }
}

class LoginRequest {
  final String email;
  final String password;
  
  LoginRequest({required this.email, required this.password});
}
```

### 4.3 Tạo Auth Service
```dart
// lib/features/auth/services/auth_service.dart
import 'package:dio/dio.dart';
import '../../../core/models/user_model.dart';
import '../models/auth_models.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();
  
  final Dio _dio = Dio();
  UserModel? _currentUser;
  String? _token;
  
  bool get isAuthenticated => _token != null && _currentUser != null;
  UserModel? get currentUser => _currentUser;
  
  Future<AuthResult> login(String email, String password) async {
    try {
      // Mock implementation - replace with real API
      await Future.delayed(Duration(seconds: 1));
      
      if (email == '<EMAIL>' && password == '123456') {
        _currentUser = UserModel(
          id: '1',
          email: email,
          name: 'Admin User',
          role: 'admin',
        );
        _token = 'mock_token_123';
        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Invalid credentials');
      }
    } catch (e) {
      return AuthResult.failure('Login failed: $e');
    }
  }
  
  Future<void> logout() async {
    _currentUser = null;
    _token = null;
  }
}
```

### 4.4 Test Auth Service
```dart
// test/auth_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_logistics/features/auth/services/auth_service.dart';

void main() {
  group('AuthService Tests', () {
    test('should login successfully with valid credentials', () async {
      final authService = AuthService();
      final result = await authService.login('<EMAIL>', '123456');
      
      expect(result.isSuccess, true);
      expect(result.user?.email, '<EMAIL>');
    });
    
    test('should fail with invalid credentials', () async {
      final authService = AuthService();
      final result = await authService.login('<EMAIL>', 'wrong');
      
      expect(result.isSuccess, false);
      expect(result.error, isNotNull);
    });
  });
}
```

### 4.5 Chạy test
```bash
flutter test
```

---

## 📱 Bước 5: Tạo Login Screen

### 5.1 Tạo Custom TextField
```dart
// lib/shared/widgets/custom_text_field.dart
import 'package:flutter/material.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String? hintText;
  final bool obscureText;
  final String? Function(String?)? validator;
  
  const CustomTextField({
    Key? key,
    required this.controller,
    required this.label,
    this.hintText,
    this.obscureText = false,
    this.validator,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: Theme.of(context).textTheme.labelMedium),
        SizedBox(height: 8),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }
}
```

### 5.2 Tạo Login Screen
```dart
// lib/features/auth/screens/login_screen.dart
import 'package:flutter/material.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../services/auth_service.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    
    final authService = AuthService();
    final result = await authService.login(
      _emailController.text,
      _passwordController.text,
    );
    
    setState(() => _isLoading = false);
    
    if (result.isSuccess) {
      // Navigate to dashboard
      Navigator.pushReplacementNamed(context, '/dashboard');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result.error ?? 'Login failed')),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Login')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              CustomTextField(
                controller: _emailController,
                label: 'Email',
                hintText: 'Enter your email',
                validator: (value) {
                  if (value?.isEmpty ?? true) return 'Email is required';
                  return null;
                },
              ),
              SizedBox(height: 16),
              CustomTextField(
                controller: _passwordController,
                label: 'Password',
                hintText: 'Enter your password',
                obscureText: true,
                validator: (value) {
                  if (value?.isEmpty ?? true) return 'Password is required';
                  return null;
                },
              ),
              SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _login,
                  child: _isLoading 
                    ? CircularProgressIndicator()
                    : Text('Login'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

### 5.3 Test Login Screen
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'shared/themes/app_theme.dart';
import 'features/auth/screens/login_screen.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Logistics',
      theme: AppTheme.lightTheme,
      home: LoginScreen(),
      routes: {
        '/dashboard': (context) => DashboardScreen(),
      },
    );
  }
}

class DashboardScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Dashboard')),
      body: Center(child: Text('Welcome to Dashboard!')),
    );
  }
}
```

### 5.4 Chạy và test
```bash
flutter run
```

**Test credentials:**
- Email: <EMAIL>
- Password: 123456

---

## 🧪 Bước 6: Testing

### 6.1 Widget Test cho Login Screen
```dart
// test/widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_logistics/features/auth/screens/login_screen.dart';

void main() {
  testWidgets('Login screen should have email and password fields', (tester) async {
    await tester.pumpWidget(
      MaterialApp(home: LoginScreen()),
    );
    
    expect(find.text('Email'), findsOneWidget);
    expect(find.text('Password'), findsOneWidget);
    expect(find.text('Login'), findsOneWidget);
  });
  
  testWidgets('Should show error for empty fields', (tester) async {
    await tester.pumpWidget(
      MaterialApp(home: LoginScreen()),
    );
    
    await tester.tap(find.text('Login'));
    await tester.pump();
    
    expect(find.text('Email is required'), findsOneWidget);
    expect(find.text('Password is required'), findsOneWidget);
  });
}
```

### 6.2 Chạy tests
```bash
flutter test
```

---

## 🚀 Bước 7: Tiếp theo

Bây giờ bạn đã có:
- ✅ Theme system
- ✅ Authentication service
- ✅ Login screen
- ✅ Basic testing

### Các bước tiếp theo:
1. **Implement GoRouter** cho navigation
2. **Tạo Order models** và services
3. **Implement QR Scanner**
4. **Tạo Dashboard** với statistics
5. **Add offline storage** với Hive
6. **Implement real API** integration

### Resources để học thêm:
- [Flutter Documentation](https://flutter.dev/docs)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)
- [Flutter Cookbook](https://flutter.dev/docs/cookbook)
- [Clean Architecture in Flutter](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)

---

## 💡 Tips

1. **Code từng bước nhỏ** - Đừng cố làm quá nhiều cùng lúc
2. **Test thường xuyên** - Chạy `flutter run` sau mỗi thay đổi
3. **Đọc error messages** - Flutter error messages rất chi tiết
4. **Use hot reload** - Nhấn `r` để reload nhanh
5. **Commit code thường xuyên** - Dùng git để backup

Happy coding! 🎉
