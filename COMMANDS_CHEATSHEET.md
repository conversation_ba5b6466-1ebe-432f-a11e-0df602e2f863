# 🚀 Flutter Commands Cheatsheet

## 📱 Flutter Commands

### Project Setup
```bash
# Tạo dự án mới
flutter create my_app --org com.example.myapp

# Kiểm tra Flutter installation
flutter doctor

# Kiểm tra Flutter version
flutter --version

# Upgrade Flutter
flutter upgrade
```

### Development
```bash
# Chạy ứng dụng
flutter run

# Chạy trên device cụ thể
flutter run -d chrome
flutter run -d android
flutter run -d ios

# Hot reload (trong khi app đang chạy)
r

# Hot restart (trong khi app đang chạy)
R

# Quit app (trong khi app đang chạy)
q
```

### Dependencies
```bash
# Cài đặt dependencies
flutter pub get

# Upgrade dependencies
flutter pub upgrade

# Add dependency
flutter pub add package_name

# Remove dependency
flutter pub remove package_name

# Check outdated packages
flutter pub outdated
```

### Build & Release
```bash
# Build APK (Android)
flutter build apk --release

# Build App Bundle (Android)
flutter build appbundle --release

# Build iOS
flutter build ios --release

# Build Web
flutter build web --release

# Clean build files
flutter clean
```

### Testing
```bash
# Chạy tất cả tests
flutter test

# Chạy test cụ thể
flutter test test/widget_test.dart

# Chạy integration tests
flutter drive --target=test_driver/app.dart

# Test coverage
flutter test --coverage
```

### Code Generation
```bash
# Generate code (cho Hive, JSON serialization, etc.)
flutter packages pub run build_runner build

# Watch for changes và auto-generate
flutter packages pub run build_runner watch

# Clean generated files
flutter packages pub run build_runner clean
```

### Devices & Emulators
```bash
# List devices
flutter devices

# List emulators
flutter emulators

# Launch emulator
flutter emulators --launch <emulator_id>

# Install app on device
flutter install
```

### Analysis & Formatting
```bash
# Analyze code
flutter analyze

# Format code
flutter format .

# Format specific file
flutter format lib/main.dart
```

### Debugging
```bash
# Run with debug info
flutter run --debug

# Run in profile mode
flutter run --profile

# Run in release mode
flutter run --release

# Attach debugger to running app
flutter attach

# Show logs
flutter logs
```

---

## 🔧 Dart Commands

### Package Management
```bash
# Get dependencies
dart pub get

# Upgrade dependencies
dart pub upgrade

# Add dependency
dart pub add package_name

# Global activate package
dart pub global activate package_name
```

### Code Analysis
```bash
# Analyze code
dart analyze

# Format code
dart format .

# Fix code issues
dart fix --apply
```

### Testing
```bash
# Run tests
dart test

# Run specific test
dart test test/my_test.dart
```

---

## 🛠 Development Workflow

### Daily Development
```bash
# 1. Pull latest changes
git pull

# 2. Get dependencies
flutter pub get

# 3. Run app
flutter run

# 4. Make changes and hot reload
# Press 'r' in terminal

# 5. Run tests
flutter test

# 6. Commit changes
git add .
git commit -m "Add new feature"
git push
```

### Before Release
```bash
# 1. Clean project
flutter clean

# 2. Get dependencies
flutter pub get

# 3. Run all tests
flutter test

# 4. Analyze code
flutter analyze

# 5. Build release
flutter build apk --release
```

---

## 🐛 Troubleshooting Commands

### Common Issues
```bash
# Gradle issues (Android)
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get

# iOS issues
cd ios
pod install
cd ..
flutter clean
flutter pub get

# Cache issues
flutter pub cache repair

# Reset Flutter
flutter channel stable
flutter upgrade --force
```

### Performance
```bash
# Profile app performance
flutter run --profile

# Analyze app size
flutter build apk --analyze-size

# Check for unused dependencies
flutter pub deps
```

---

## 📱 Device-Specific Commands

### Android
```bash
# List Android devices
adb devices

# Install APK manually
adb install build/app/outputs/flutter-apk/app-release.apk

# View logs
adb logcat

# Clear app data
adb shell pm clear com.example.myapp
```

### iOS
```bash
# List iOS devices
xcrun xctrace list devices

# Open iOS Simulator
open -a Simulator

# View iOS logs
xcrun simctl spawn booted log stream --predicate 'process == "Runner"'
```

---

## 🔍 Useful Shortcuts

### VS Code
- `Ctrl/Cmd + Shift + P`: Command palette
- `F5`: Start debugging
- `Ctrl/Cmd + F5`: Run without debugging
- `Ctrl/Cmd + .`: Quick fix
- `Alt + Shift + F`: Format document

### Android Studio
- `Shift + F10`: Run
- `Shift + F9`: Debug
- `Ctrl/Cmd + Alt + L`: Format code
- `Alt + Enter`: Quick fix

---

## 📊 Monitoring Commands

### Performance
```bash
# Monitor app performance
flutter run --profile --trace-startup

# Memory usage
flutter run --profile --trace-systrace

# Widget inspector
flutter inspector
```

### Logs
```bash
# Flutter logs
flutter logs

# Verbose logs
flutter logs -v

# Filter logs
flutter logs | grep "MyTag"
```

---

## 🚀 Deployment Commands

### Android Play Store
```bash
# Build App Bundle
flutter build appbundle --release

# Upload to Play Console
# (Use Play Console web interface)
```

### iOS App Store
```bash
# Build iOS
flutter build ios --release

# Archive in Xcode
# (Open ios/Runner.xcworkspace in Xcode)
```

### Web Deployment
```bash
# Build web
flutter build web --release

# Deploy to Firebase Hosting
firebase deploy

# Deploy to GitHub Pages
# (Copy build/web/* to gh-pages branch)
```

---

## 💡 Pro Tips

1. **Use aliases** for common commands:
```bash
alias fr="flutter run"
alias ft="flutter test"
alias fpg="flutter pub get"
alias fc="flutter clean"
```

2. **Create scripts** for complex workflows:
```bash
# scripts/dev.sh
#!/bin/bash
flutter clean
flutter pub get
flutter run
```

3. **Use Makefile** for project automation:
```makefile
# Makefile
.PHONY: run test build clean

run:
	flutter run

test:
	flutter test

build:
	flutter build apk --release

clean:
	flutter clean
	flutter pub get
```

Happy coding! 🎉
