# 📱 Flutter Learning Guide - Hướng dẫn học Flutter toàn diện

## 🎯 Mục tiêu học tập
Sau khi hoàn thành guide này, bạn sẽ có thể:
- <PERSON><PERSON><PERSON> cơ bản về Flutter và Dart
- X<PERSON>y dựng ứng dụng mobile hoàn chỉnh
- Quản lý state và navigation
- Tích hợp API và database
- Deploy ứng dụng lên store

---

## 📚 Phần 1: <PERSON>ế<PERSON> thức nền tảng

### 🔤 **Dart Programming Language**

#### **1.1 Cú pháp cơ bản**
```dart
// Variables
String name = 'Flutter';
int age = 5;
double price = 99.99;
bool isActive = true;
var dynamicVar = 'Can be anything';

// Lists & Maps
List<String> fruits = ['apple', 'banana', 'orange'];
Map<String, int> scores = {'Alice': 100, 'Bob': 85};

// Functions
String greet(String name) {
  return 'Hello, $name!';
}

// Arrow functions
String greetShort(String name) => 'Hello, $name!';
```

#### **1.2 Classes và Objects**
```dart
class Person {
  String name;
  int age;
  
  // Constructor
  Person(this.name, this.age);
  
  // Named constructor
  Person.baby(this.name) : age = 0;
  
  // Methods
  void introduce() {
    print('Hi, I\'m $name, $age years old');
  }
  
  // Getters & Setters
  String get info => '$name ($age)';
  set updateAge(int newAge) => age = newAge;
}

// Inheritance
class Student extends Person {
  String school;
  
  Student(String name, int age, this.school) : super(name, age);
  
  @override
  void introduce() {
    super.introduce();
    print('I study at $school');
  }
}
```

#### **1.3 Async Programming**
```dart
// Future
Future<String> fetchData() async {
  await Future.delayed(Duration(seconds: 2));
  return 'Data loaded';
}

// Using async/await
void loadData() async {
  try {
    String data = await fetchData();
    print(data);
  } catch (e) {
    print('Error: $e');
  }
}

// Stream
Stream<int> countStream() async* {
  for (int i = 1; i <= 5; i++) {
    await Future.delayed(Duration(seconds: 1));
    yield i;
  }
}
```

---

## 🏗 Phần 2: Flutter Widgets

### 🧱 **2.1 Stateless vs Stateful Widgets**

#### **Stateless Widget (Không thay đổi)**
```dart
class WelcomeScreen extends StatelessWidget {
  final String title;
  
  const WelcomeScreen({Key? key, required this.title}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(
        child: Text('Welcome to Flutter!'),
      ),
    );
  }
}
```

#### **Stateful Widget (Có thể thay đổi)**
```dart
class Counter extends StatefulWidget {
  @override
  _CounterState createState() => _CounterState();
}

class _CounterState extends State<Counter> {
  int _count = 0;
  
  void _increment() {
    setState(() {
      _count++;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Count: $_count'),
            ElevatedButton(
              onPressed: _increment,
              child: Text('Increment'),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 🎨 **2.2 Layout Widgets**

#### **Column & Row**
```dart
Column(
  mainAxisAlignment: MainAxisAlignment.center,
  crossAxisAlignment: CrossAxisAlignment.start,
  children: [
    Text('Item 1'),
    Text('Item 2'),
    Text('Item 3'),
  ],
)

Row(
  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  children: [
    Icon(Icons.home),
    Icon(Icons.search),
    Icon(Icons.person),
  ],
)
```

#### **Container & Padding**
```dart
Container(
  width: 200,
  height: 100,
  padding: EdgeInsets.all(16),
  margin: EdgeInsets.symmetric(vertical: 8),
  decoration: BoxDecoration(
    color: Colors.blue,
    borderRadius: BorderRadius.circular(8),
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withOpacity(0.5),
        spreadRadius: 2,
        blurRadius: 5,
      ),
    ],
  ),
  child: Text('Hello Flutter'),
)
```

#### **ListView & GridView**
```dart
// ListView
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(
      title: Text(items[index].title),
      subtitle: Text(items[index].description),
      onTap: () => print('Tapped ${items[index].title}'),
    );
  },
)

// GridView
GridView.builder(
  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
    crossAxisCount: 2,
    crossAxisSpacing: 8,
    mainAxisSpacing: 8,
  ),
  itemCount: items.length,
  itemBuilder: (context, index) {
    return Card(
      child: Center(child: Text(items[index])),
    );
  },
)
```

---

## 🎛 Phần 3: State Management

### 📦 **3.1 setState (Cơ bản)**
```dart
class TodoApp extends StatefulWidget {
  @override
  _TodoAppState createState() => _TodoAppState();
}

class _TodoAppState extends State<TodoApp> {
  List<String> todos = [];
  TextEditingController _controller = TextEditingController();
  
  void _addTodo() {
    if (_controller.text.isNotEmpty) {
      setState(() {
        todos.add(_controller.text);
        _controller.clear();
      });
    }
  }
  
  void _removeTodo(int index) {
    setState(() {
      todos.removeAt(index);
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Todo App')),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: InputDecoration(hintText: 'Enter todo'),
                  ),
                ),
                IconButton(
                  onPressed: _addTodo,
                  icon: Icon(Icons.add),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: todos.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(todos[index]),
                  trailing: IconButton(
                    onPressed: () => _removeTodo(index),
                    icon: Icon(Icons.delete),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
```

### 🔄 **3.2 Provider Pattern**
```dart
// Model
class Counter extends ChangeNotifier {
  int _count = 0;
  
  int get count => _count;
  
  void increment() {
    _count++;
    notifyListeners();
  }
  
  void decrement() {
    _count--;
    notifyListeners();
  }
}

// Main App
void main() {
  runApp(
    ChangeNotifierProvider(
      create: (context) => Counter(),
      child: MyApp(),
    ),
  );
}

// Using Provider
class CounterScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Consumer<Counter>(
              builder: (context, counter, child) {
                return Text('Count: ${counter.count}');
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () => context.read<Counter>().decrement(),
                  child: Text('-'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () => context.read<Counter>().increment(),
                  child: Text('+'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

---

## 🧭 Phần 4: Navigation

### 🗺 **4.1 Basic Navigation**
```dart
// Navigate to new screen
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => SecondScreen()),
);

// Navigate back
Navigator.pop(context);

// Navigate with data
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => DetailScreen(data: 'Hello'),
  ),
);

// Replace current screen
Navigator.pushReplacement(
  context,
  MaterialPageRoute(builder: (context) => HomeScreen()),
);
```

### 🛣 **4.2 Named Routes**
```dart
// Define routes in MaterialApp
MaterialApp(
  initialRoute: '/',
  routes: {
    '/': (context) => HomeScreen(),
    '/profile': (context) => ProfileScreen(),
    '/settings': (context) => SettingsScreen(),
  },
)

// Navigate using named routes
Navigator.pushNamed(context, '/profile');
Navigator.pushReplacementNamed(context, '/home');
```

### 🎯 **4.3 Go Router (Modern approach)**
```dart
// pubspec.yaml
dependencies:
  go_router: ^10.0.0

// Router configuration
final GoRouter _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => HomeScreen(),
    ),
    GoRoute(
      path: '/profile/:userId',
      builder: (context, state) {
        final userId = state.pathParameters['userId']!;
        return ProfileScreen(userId: userId);
      },
    ),
  ],
);

// Usage
context.go('/profile/123');
context.push('/settings');
```

---

## 🌐 Phần 5: HTTP & API Integration

### 📡 **5.1 HTTP Requests**
```dart
// pubspec.yaml
dependencies:
  http: ^1.1.0

// API Service
import 'package:http/http.dart' as http;
import 'dart:convert';

class ApiService {
  static const String baseUrl = 'https://jsonplaceholder.typicode.com';
  
  // GET request
  static Future<List<Post>> getPosts() async {
    final response = await http.get(Uri.parse('$baseUrl/posts'));
    
    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(response.body);
      return data.map((json) => Post.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load posts');
    }
  }
  
  // POST request
  static Future<Post> createPost(Post post) async {
    final response = await http.post(
      Uri.parse('$baseUrl/posts'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(post.toJson()),
    );
    
    if (response.statusCode == 201) {
      return Post.fromJson(json.decode(response.body));
    } else {
      throw Exception('Failed to create post');
    }
  }
}

// Model
class Post {
  final int id;
  final String title;
  final String body;
  
  Post({required this.id, required this.title, required this.body});
  
  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'],
      title: json['title'],
      body: json['body'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
    };
  }
}
```

### 📱 **5.2 Using FutureBuilder**
```dart
class PostsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Posts')),
      body: FutureBuilder<List<Post>>(
        future: ApiService.getPosts(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(child: Text('No posts found'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final post = snapshot.data![index];
                return ListTile(
                  title: Text(post.title),
                  subtitle: Text(post.body),
                );
              },
            );
          }
        },
      ),
    );
  }
}
```

---

## 💾 Phần 6: Local Storage

### 🗃 **6.1 SharedPreferences**
```dart
// pubspec.yaml
dependencies:
  shared_preferences: ^2.2.0

// Usage
import 'package:shared_preferences/shared_preferences.dart';

class PrefsService {
  static Future<void> saveString(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }
  
  static Future<String?> getString(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }
  
  static Future<void> saveBool(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }
  
  static Future<bool> getBool(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? false;
  }
}
```

### 🗄 **6.2 SQLite Database**
```dart
// pubspec.yaml
dependencies:
  sqflite: ^2.3.0
  path: ^1.8.3

// Database Helper
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'app_database.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }
  
  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE users(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');
  }
  
  // CRUD Operations
  Future<int> insertUser(Map<String, dynamic> user) async {
    final db = await database;
    return await db.insert('users', user);
  }
  
  Future<List<Map<String, dynamic>>> getUsers() async {
    final db = await database;
    return await db.query('users');
  }
  
  Future<int> updateUser(int id, Map<String, dynamic> user) async {
    final db = await database;
    return await db.update('users', user, where: 'id = ?', whereArgs: [id]);
  }
  
  Future<int> deleteUser(int id) async {
    final db = await database;
    return await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }
}
```

---

## 🎨 Phần 7: UI/UX Best Practices

### 🌈 **7.1 Theming**
```dart
// Custom Theme
ThemeData customTheme = ThemeData(
  primarySwatch: Colors.blue,
  primaryColor: Color(0xFF2196F3),
  accentColor: Color(0xFFFF4081),
  backgroundColor: Color(0xFFF5F5F5),
  cardColor: Colors.white,
  textTheme: TextTheme(
    headline1: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
    headline2: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
    bodyText1: TextStyle(fontSize: 16, color: Colors.black87),
    bodyText2: TextStyle(fontSize: 14, color: Colors.black54),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
  ),
);

// Apply theme
MaterialApp(
  theme: customTheme,
  home: HomeScreen(),
)
```

### 📱 **7.2 Responsive Design**
```dart
class ResponsiveWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > 600) {
          // Tablet/Desktop layout
          return Row(
            children: [
              Expanded(flex: 1, child: Sidebar()),
              Expanded(flex: 2, child: MainContent()),
            ],
          );
        } else {
          // Mobile layout
          return Column(
            children: [
              AppBar(title: Text('Mobile Layout')),
              Expanded(child: MainContent()),
            ],
          );
        }
      },
    );
  }
}

// Media Query
class AdaptiveText extends StatelessWidget {
  final String text;
  
  const AdaptiveText(this.text);
  
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final fontSize = screenWidth > 600 ? 24.0 : 16.0;
    
    return Text(
      text,
      style: TextStyle(fontSize: fontSize),
    );
  }
}
```

---

## 🧪 Phần 8: Testing

### ✅ **8.1 Unit Tests**
```dart
// test/counter_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/counter.dart';

void main() {
  group('Counter Tests', () {
    test('Counter should start at 0', () {
      final counter = Counter();
      expect(counter.value, 0);
    });
    
    test('Counter should increment', () {
      final counter = Counter();
      counter.increment();
      expect(counter.value, 1);
    });
    
    test('Counter should decrement', () {
      final counter = Counter();
      counter.increment();
      counter.decrement();
      expect(counter.value, 0);
    });
  });
}
```

### 🎭 **8.2 Widget Tests**
```dart
// test/widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/main.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(MyApp());
    
    // Verify that counter starts at 0
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);
    
    // Tap the '+' icon and trigger a frame
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();
    
    // Verify that counter has incremented
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });
}
```

---

## 📦 Phần 9: Popular Packages

### 🔧 **Essential Packages**
```yaml
dependencies:
  # State Management
  provider: ^6.0.5
  bloc: ^8.1.2
  riverpod: ^2.4.0
  
  # Navigation
  go_router: ^10.0.0
  auto_route: ^7.7.1
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.0
  retrofit: ^4.0.1
  
  # Local Storage
  shared_preferences: ^2.2.0
  hive: ^2.2.3
  sqflite: ^2.3.0
  
  # UI Components
  cached_network_image: ^3.2.3
  shimmer: ^3.0.0
  lottie: ^2.6.0
  
  # Utilities
  intl: ^0.18.1
  uuid: ^3.0.7
  path_provider: ^2.1.0
  
  # Firebase
  firebase_core: ^2.15.0
  firebase_auth: ^4.7.2
  cloud_firestore: ^4.8.4
```

---

## 🚀 Phần 10: Deployment

### 📱 **10.1 Build for Production**
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release

# Web
flutter build web --release
```

### 🏪 **10.2 App Store Deployment**
```yaml
# android/app/build.gradle
android {
    compileSdkVersion 33
    
    defaultConfig {
        applicationId "com.example.myapp"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0.0"
    }
    
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
}
```

---

## 📖 Tài liệu tham khảo

### 🌐 **Official Resources**
- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)
- [Flutter Widget Catalog](https://docs.flutter.dev/development/ui/widgets)

### 📺 **Video Tutorials**
- Flutter Official YouTube Channel
- The Net Ninja Flutter Tutorial
- Academind Flutter Course

### 📚 **Books**
- "Flutter in Action" by Eric Windmill
- "Beginning Flutter" by Marco Napoli
- "Flutter Complete Reference" by Alberto Miola

### 🛠 **Tools & Extensions**
- **VS Code**: Flutter & Dart extensions
- **Android Studio**: Flutter plugin
- **Flutter Inspector**: UI debugging
- **Dart DevTools**: Performance profiling

---

## 🎯 Learning Path Roadmap

### **Beginner (1-2 months)**
1. ✅ Dart basics
2. ✅ Flutter widgets
3. ✅ Basic layouts
4. ✅ Simple navigation
5. ✅ setState management

### **Intermediate (2-3 months)**
1. ✅ Advanced widgets
2. ✅ HTTP requests
3. ✅ Local storage
4. ✅ Provider/Bloc
5. ✅ Custom widgets

### **Advanced (3-6 months)**
1. ✅ Complex state management
2. ✅ Testing
3. ✅ Performance optimization
4. ✅ Platform integration
5. ✅ Deployment

---

## 🔥 Phần 11: Advanced Topics

### 🏗 **11.1 Custom Widgets**
```dart
// Reusable Custom Button
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color? color;
  final bool isLoading;

  const CustomButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.color,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color ?? Theme.of(context).primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                text,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}

// Usage
CustomButton(
  text: 'Login',
  onPressed: () => _handleLogin(),
  isLoading: _isLoading,
)
```

### 🎨 **11.2 Animations**
```dart
// Basic Animation
class AnimatedContainer extends StatefulWidget {
  @override
  _AnimatedContainerState createState() => _AnimatedContainerState();
}

class _AnimatedContainerState extends State<AnimatedContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: Container(
            width: 200,
            height: 200,
            color: Colors.blue,
            child: Center(child: Text('Animated!')),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

// Hero Animation
class HeroAnimation extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => DetailPage()),
          );
        },
        child: Hero(
          tag: 'hero-image',
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
}
```

### 🔌 **11.3 Platform Channels**
```dart
// Method Channel for native integration
import 'package:flutter/services.dart';

class PlatformService {
  static const MethodChannel _channel = MethodChannel('com.example/platform');

  static Future<String> getBatteryLevel() async {
    try {
      final int result = await _channel.invokeMethod('getBatteryLevel');
      return 'Battery level: $result%';
    } on PlatformException catch (e) {
      return 'Failed to get battery level: ${e.message}';
    }
  }

  static Future<void> showNativeAlert(String message) async {
    try {
      await _channel.invokeMethod('showAlert', {'message': message});
    } on PlatformException catch (e) {
      print('Failed to show alert: ${e.message}');
    }
  }
}
```

---

## 🧠 Phần 12: Performance Optimization

### ⚡ **12.1 Widget Optimization**
```dart
// Use const constructors
class OptimizedWidget extends StatelessWidget {
  const OptimizedWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Column(
      children: [
        Text('Static text'), // const
        Icon(Icons.star),    // const
      ],
    );
  }
}

// Avoid rebuilding expensive widgets
class ExpensiveWidget extends StatelessWidget {
  final String data;

  const ExpensiveWidget({Key? key, required this.data}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text(data),
    );
  }
}

// Use RepaintBoundary for complex widgets
RepaintBoundary(
  child: ComplexCustomPainter(),
)

// ListView optimization
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(
      key: ValueKey(items[index].id), // Add keys
      title: Text(items[index].title),
    );
  },
)
```

### 📊 **12.2 Memory Management**
```dart
// Dispose controllers and streams
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  late TextEditingController _controller;
  late StreamSubscription _subscription;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _subscription = someStream.listen((data) {
      // Handle data
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _subscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(controller: _controller);
  }
}

// Use AutomaticKeepAliveClientMixin for expensive widgets
class KeepAliveWidget extends StatefulWidget {
  @override
  _KeepAliveWidgetState createState() => _KeepAliveWidgetState();
}

class _KeepAliveWidgetState extends State<KeepAliveWidget>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Must call super.build
    return ExpensiveWidget();
  }
}
```

---

## 🔐 Phần 13: Security Best Practices

### 🛡 **13.1 Data Security**
```dart
// Secure storage
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorage {
  static const _storage = FlutterSecureStorage();

  static Future<void> storeToken(String token) async {
    await _storage.write(key: 'auth_token', value: token);
  }

  static Future<String?> getToken() async {
    return await _storage.read(key: 'auth_token');
  }

  static Future<void> deleteToken() async {
    await _storage.delete(key: 'auth_token');
  }
}

// Input validation
class Validator {
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Enter a valid email';
    }
    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
      return 'Password must contain uppercase, lowercase and number';
    }
    return null;
  }
}
```

### � **13.2 Network Security**
```dart
// Certificate pinning
import 'package:dio/dio.dart';
import 'package:dio_certificate_pinning/dio_certificate_pinning.dart';

class SecureApiService {
  late Dio _dio;

  SecureApiService() {
    _dio = Dio();
    _dio.interceptors.add(
      CertificatePinningInterceptor(
        allowedSHAFingerprints: ['SHA256:XXXXXX'], // Your certificate fingerprint
      ),
    );
  }

  Future<Response> secureGet(String endpoint) async {
    return await _dio.get(endpoint);
  }
}

// Request encryption
import 'dart:convert';
import 'package:crypto/crypto.dart';

class ApiSecurity {
  static String generateSignature(String data, String secret) {
    var key = utf8.encode(secret);
    var bytes = utf8.encode(data);
    var hmacSha256 = Hmac(sha256, key);
    var digest = hmacSha256.convert(bytes);
    return digest.toString();
  }

  static Map<String, String> getSecureHeaders(String body) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final signature = generateSignature('$timestamp$body', 'your-secret-key');

    return {
      'Content-Type': 'application/json',
      'X-Timestamp': timestamp,
      'X-Signature': signature,
    };
  }
}
```

---

## 🧪 Phần 14: Advanced Testing

### 🎯 **14.1 Integration Tests**
```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:myapp/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Tests', () {
    testWidgets('Complete user flow test', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test login flow
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('password_field')), 'password123');
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();

      // Verify navigation to home screen
      expect(find.text('Welcome'), findsOneWidget);

      // Test navigation
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();

      // Verify profile screen
      expect(find.text('Profile'), findsOneWidget);
    });
  });
}
```

### 🔍 **14.2 Golden Tests**
```dart
// test/golden_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/custom_button.dart';

void main() {
  testWidgets('CustomButton golden test', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: CustomButton(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      ),
    );

    await expectLater(
      find.byType(CustomButton),
      matchesGoldenFile('golden/custom_button.png'),
    );
  });
}
```

---

## 🌍 Phần 15: Internationalization (i18n)

### 🗣 **15.1 Multi-language Support**
```dart
// pubspec.yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1

// l10n.yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart

// lib/l10n/app_en.arb
{
  "hello": "Hello",
  "welcome": "Welcome to Flutter",
  "login": "Login",
  "email": "Email",
  "password": "Password",
  "greeting": "Hello {name}",
  "@greeting": {
    "placeholders": {
      "name": {
        "type": "String"
      }
    }
  }
}

// lib/l10n/app_vi.arb
{
  "hello": "Xin chào",
  "welcome": "Chào mừng đến với Flutter",
  "login": "Đăng nhập",
  "email": "Email",
  "password": "Mật khẩu",
  "greeting": "Xin chào {name}"
}

// main.dart
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

MaterialApp(
  localizationsDelegates: [
    AppLocalizations.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  supportedLocales: [
    Locale('en', ''),
    Locale('vi', ''),
  ],
  home: HomeScreen(),
)

// Usage in widgets
class HomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text(l10n.welcome)),
      body: Column(
        children: [
          Text(l10n.hello),
          Text(l10n.greeting('Flutter')),
          ElevatedButton(
            onPressed: () {},
            child: Text(l10n.login),
          ),
        ],
      ),
    );
  }
}
```

---

## 📱 Phần 16: Platform-Specific Features

### 🤖 **16.1 Android Integration**
```dart
// android/app/src/main/kotlin/MainActivity.kt
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example/platform"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getBatteryLevel" -> {
                    val batteryLevel = getBatteryLevel()
                    if (batteryLevel != -1) {
                        result.success(batteryLevel)
                    } else {
                        result.error("UNAVAILABLE", "Battery level not available.", null)
                    }
                }
                else -> result.notImplemented()
            }
        }
    }

    private fun getBatteryLevel(): Int {
        val batteryManager = getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    }
}
```

### 🍎 **16.2 iOS Integration**
```swift
// ios/Runner/AppDelegate.swift
import UIKit
import Flutter

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        let platformChannel = FlutterMethodChannel(name: "com.example/platform",
                                                  binaryMessenger: controller.binaryMessenger)

        platformChannel.setMethodCallHandler({
            (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            switch call.method {
            case "getBatteryLevel":
                self.receiveBatteryLevel(result: result)
            default:
                result(FlutterMethodNotImplemented)
            }
        })

        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    private func receiveBatteryLevel(result: FlutterResult) {
        let device = UIDevice.current
        device.isBatteryMonitoringEnabled = true
        if device.batteryState == UIDevice.BatteryState.unknown {
            result(FlutterError(code: "UNAVAILABLE",
                              message: "Battery level not available.",
                              details: nil))
        } else {
            result(Int(device.batteryLevel * 100))
        }
    }
}
```

---

## 🎓 Phần 17: Learning Projects

### 📝 **17.1 Todo App (Beginner)**
**Features to implement:**
- Add/Edit/Delete tasks
- Mark as complete
- Filter by status
- Local storage with SQLite
- Dark/Light theme

### 🛒 **17.2 E-commerce App (Intermediate)**
**Features to implement:**
- Product catalog
- Shopping cart
- User authentication
- Payment integration
- Order history
- Push notifications

### 💬 **17.3 Chat App (Advanced)**
**Features to implement:**
- Real-time messaging
- User presence
- File sharing
- Group chats
- End-to-end encryption
- Voice/Video calls

---

## 🏆 Phần 18: Career Path

### 💼 **18.1 Job Opportunities**
- **Mobile App Developer**
- **Flutter Developer**
- **Cross-platform Developer**
- **Full-stack Developer**
- **Technical Lead**
- **Flutter Consultant**

### 📈 **18.2 Skill Development**
1. **Technical Skills:**
   - Advanced Dart programming
   - State management patterns
   - Testing strategies
   - Performance optimization
   - CI/CD pipelines

2. **Soft Skills:**
   - Problem-solving
   - Communication
   - Team collaboration
   - Project management
   - Continuous learning

### 🌟 **18.3 Portfolio Projects**
- Personal expense tracker
- Weather app with animations
- Social media clone
- E-learning platform
- IoT dashboard
- AR/VR integration

---

## 🎯 Final Tips for Success

### 📚 **Learning Strategy**
1. **Practice Daily**: Code every day, even 30 minutes
2. **Build Projects**: Apply what you learn immediately
3. **Join Communities**: Flutter Discord, Reddit, Stack Overflow
4. **Read Code**: Study open-source Flutter projects
5. **Stay Updated**: Follow Flutter releases and updates

### 🚀 **Next Steps**
1. Complete the beginner projects
2. Contribute to open-source projects
3. Build your portfolio
4. Network with other developers
5. Consider Flutter certifications

### 🎉 **Remember**
- **Be Patient**: Learning takes time
- **Practice Consistently**: Regular practice beats intensive cramming
- **Ask Questions**: Don't hesitate to seek help
- **Enjoy the Journey**: Have fun while learning!

**Happy Learning! �🚀 Chúc bạn học Flutter thành công và trở thành Flutter Developer giỏi!**
